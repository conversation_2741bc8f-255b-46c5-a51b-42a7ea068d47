"use client";

import { useRouter } from "next/navigation";
import { useFormik } from "formik";
import * as Yup from "yup";
import Image from "next/image";
import OTPInput from "react-otp-input";
import {
    Box,
    Button,
    TextField,
    Typography,
    Link,
} from "@mui/material";
import SigninBG from "../../assests/Signinbg.png";
import signinother from "../../assests/signin.png";
import { useState, useEffect } from "react";
import dynamic from "next/dynamic";
const QRCodePopup = dynamic(() => import("./QRCodePopup"), { ssr: false });
const Loader = dynamic(() => import("../../components/loader/loader"), { ssr: false });
import { requestOtp, verifyOtp, verifyTOtp } from "../../services/apiService";
import { toast } from "react-toastify";
import { useAppDispatch, useAppSelector } from "../../store/store";
import { fetchUserById } from "../../store/userthunk";
import { getPermissionData } from "../../services/rolesapiService";
import { setAccessRightsList } from "../../store/UserPermissionSlice";
import type { RootState } from "../../store/store";
import { AllMenuItems } from "./paths";
import { updateFcmToken } from "../../services/updateToken";

// const moduleNameToPathMap: Record<string, string> = {
//     Dashboard: "/dashboard",
//     Organizations: "/organizations",
//     "Project Dockets": "/project-dockets",
//     Datasets: "/datasets",
//     "User Management": "/user-management",
//     "Audit Logs": "/audit-logs",
//     Forms: "/forms",
//     "Request Approval": "/request-approval",
//     "Role & Access": "/role-access",
//     Settings: "/settings",
// };

export default function LandingPage() {
    const router = useRouter();
    const dispatch = useAppDispatch();

    const [otpSent, setOtpSent] = useState(false);
    const [isMobile, setIsMobile] = useState(false);
    const [showQRPopup, setShowQRPopup] = useState(false);
    const [mfastatus, setMfastatus] = useState(false);
    const [qrimage, setQrImage] = useState("");
    const [loading, setLoading] = useState(false);
    const [verifiedEmail, setVerifiedEmail] = useState("");
    const [shouldRedirect, setShouldRedirect] = useState(false);
    const accessModules = useAppSelector((state: RootState) =>
        state?.accessRights?.accessRightsList?.modules?.map((mod) => mod?.module_name)
    );

    useEffect(() => {
        const handleResize = () => setIsMobile(window.innerWidth <= 768);
        handleResize();
        window?.addEventListener("resize", handleResize);
        return () => window.removeEventListener("resize", handleResize);
    }, []);

    useEffect(() => {
        if (shouldRedirect && accessModules && accessModules.length > 0) {
            
            const filteredMenuItems = AllMenuItems.filter((item) =>
                accessModules.includes(item.label),
            );
            

            const defaultModulePath = filteredMenuItems?.[0]?.path || "/";
            router.push(defaultModulePath);
        }
    }, [accessModules, shouldRedirect]);
    const handleRequestOtp = async () => {
        if (loading) return;
        setLoading(true);
        try {
            const res = await requestOtp(formik.values.email);
            if (res.data) {
                toast.success(res.data.message || "OTP sent successfully");
                setMfastatus(res.data.is_mfa_enabled);
                setOtpSent(true);
            }
        } catch (err) {
            toast.error(err?.response?.data?.message);
        } finally {
            setLoading(false);
        }
    };

    const formik = useFormik({
        initialValues: { email: "", otp: "" },
        validationSchema: Yup.object().shape({
            email: Yup.string()
                .email("Invalid email format")
                .required("Email is required"),
            otp: otpSent
                ? Yup.string()
                    .matches(/^\d{6}$/, "OTP must be exactly 6 digits")
                    .required("OTP is required")
                : Yup.string().notRequired(),
        }),
        enableReinitialize: true,
        onSubmit: async (values, { setSubmitting, resetForm }) => {
            if (loading) return;
            setLoading(true);

            try {
                if (!otpSent) {
                    await handleRequestOtp();
                    return;
                }

                let res;
                if (mfastatus) {
                    res = await verifyTOtp({ email: values.email, otp: values.otp });
                } else {
                    res = await verifyOtp({ email: values.email, otp: values.otp });
                }

                toast.success(res.data.message || "OTP verified");

                if (mfastatus && res?.data?.user_id && res?.data?.role_id) {
                    dispatch(fetchUserById(res?.data?.user_id));
                    const Permission = await getPermissionData(res?.data?.role_id);
                    Permission.data.modules.push({
                        module_id: "0e882c3f-74da-4386-8633-76f2d7e6cdm6",
                        module_name: "profile",
                        permissions: [
                            { permission_id: "read", permission_name: "read" },
                            { permission_id: "update", permission_name: "update" },
                        ],
                    });
                    dispatch(setAccessRightsList(Permission.data));
                    await updateFcmToken();
                    setShouldRedirect(true);
                }

                if (!mfastatus && res.data.qr_image) {
                    setVerifiedEmail(values.email);
                    setQrImage(res.data.qr_image);
                    setShowQRPopup(true);
                } else {

                    const filteredMenuItems = AllMenuItems.filter((item) =>
                        accessModules.includes(item.label),
                    );
                    const defaultModuleName = filteredMenuItems?.[0]?.path || "/";
                    router.push(defaultModuleName);

                }

                resetForm({
                    values: {
                        email: values.email,
                        otp: "",
                    },
                });
            } catch (err) {
                toast.error(err?.response?.data?.message);
            } finally {
                setLoading(false);
                setSubmitting(false);
            }
        },
    });

    return (
        <>
            {loading && <Loader />}
            {qrimage !== "" && (
                <QRCodePopup
                    open={showQRPopup}
                    onClose={() => setShowQRPopup(false)}
                    setShowQRPopup={setShowQRPopup}
                    qrimage={qrimage}
                    email={verifiedEmail}
                />
            )}
            <div className="grid grid-cols-12 min-h-screen relative">
                <div className="col-span-12 md:col-span-7 relative w-full h-full">
                    <Image
                        src={SigninBG}
                        alt="Signin Background"
                        layout="fill"
                        objectFit="cover"
                        className="z-0"
                    />
                </div>

                <div className="col-span-12 md:col-span-5 relative flex items-center justify-center">
                    {!isMobile && (
                        <Image
                            src={signinother}
                            alt="Form Background"
                            layout="fill"
                            objectFit="cover"
                            className="absolute z-0"
                        />
                    )}

                    <Box className="z-10 w-full max-w-md p-6 md:p-10 bg-white bg-opacity-90 backdrop-blur-md rounded-xl shadow-xl">
                        <Typography variant="h5" fontWeight="bold" gutterBottom style={{ color: '#000000' }}>
                            Welcome to AIMx
                        </Typography>
                        <Typography variant="body2" color="textSecondary" gutterBottom>
                            Become part of the machine learning community!
                        </Typography>

                        <Box component="form" onSubmit={formik.handleSubmit} mt={2}>
                            <Typography variant="subtitle1" fontWeight="bold" style={{ color: '#000000' }} gutterBottom>
                                Email Address
                            </Typography>
                            <TextField
                                fullWidth
                                type="email"
                                variant="outlined"
                                placeholder="<EMAIL>"
                                margin="normal"
                                name="email"
                                value={formik.values.email}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                error={formik.touched.email && Boolean(formik.errors.email)}
                                helperText={formik.touched.email && formik.errors.email}
                                disabled={otpSent}
                            />

                            {otpSent && (
                                <>
                                    <Typography variant="subtitle1" fontWeight="bold" mt={2} gutterBottom style={{ color: '#000000' }}>
                                        Enter the Six-Digit Code
                                    </Typography>
                                    <Box display="flex" justifyContent="center">
                                        <OTPInput
                                            value={formik.values.otp || ""}
                                            onChange={(otpValue) => {
                                                const numericOtp = otpValue.replace(/\D/g, "");
                                                const trimmedOtp = numericOtp.slice(0, 6);
                                                formik.setFieldValue("otp", trimmedOtp);
                                            }}
                                            numInputs={6}
                                            renderInput={(props) => (
                                                <input
                                                    {...props}
                                                    className="otp-input"
                                                    type="tel"
                                                    inputMode="numeric"
                                                />
                                            )}
                                            inputStyle={{
                                                width: "45px",
                                                height: "45px",
                                                margin: "10px",
                                                fontSize: "18px",
                                                border: "1px solid #ccc",
                                                borderRadius: "10px",
                                                textAlign: "center",
                                                color: '#000000'
                                            }}
                                        />
                                    </Box>
                                    <Box mt={1} display="flex" alignItems="center" flexWrap="wrap">
                                        {formik.touched.otp && formik.errors.otp && (
                                            <Typography component="span" color="error" variant="caption" sx={{ mr: 1 }}>
                                                {formik.errors.otp}
                                            </Typography>
                                        )}
                                        {otpSent && (
                                            <Typography component="span" variant="caption" color="textSecondary">
                                                {mfastatus
                                                    ? "Hint: Open your Microsoft Authenticator app to view the OTP"
                                                    : "Hint: Please check your email for the OTP."}
                                            </Typography>
                                        )}
                                    </Box>
                                    {!mfastatus && (
                                        <Box display="flex" justifyContent="flex-end" mt={1}>
                                            <Link
                                                href="#"
                                                variant="body2"
                                                color="primary"
                                                onClick={async (e) => {
                                                    e.preventDefault();
                                                    await handleRequestOtp();
                                                }}
                                            >
                                                Resend Code
                                            </Link>
                                        </Box>
                                    )}
                                </>
                            )}

                            <Button
                                fullWidth
                                type="submit"
                                variant="contained"
                                className="text-transform-none"
                                sx={{
                                    mt: 3,
                                    py: 1.5,
                                    fontSize: "18px",
                                    fontWeight: "bold",
                                    height: "50px",
                                    background: "linear-gradient(to right, #FF512F, #F09819)",
                                    color: "white",
                                    borderRadius: "6px",
                                    boxShadow: "0px 4px 10px rgba(255, 81, 47, 0.3)",
                                    "&:hover": {
                                        background: "linear-gradient(to right, #F09819, #FF512F)",
                                    },
                                }}
                            >
                                {otpSent ? "Verify" : "Authorize"}
                            </Button>
                        </Box>
                    </Box>
                </div>
            </div>
        </>
    );
}