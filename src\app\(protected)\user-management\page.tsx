"use client";

import type React from "react";
import { useState, useEffect } from "react";
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
} from "@mui/material";
import SearchFilter from "../../../components/ui/SearchFilter";
import { tailwindStyles } from "../../../styles/tailwindStyles";
import userManagementTableHeaders from "../../../components/ui/tableHeaders";
import { deactivateUser, getUser, activateUser } from "../../../services/userService";
import TablePagination from "../../../components/ui/Pagination";
import UserManagementFilter from "../../../components/ui/UserManagementFilter";
import ConfirmationDialog from "../../../components/common/ConfirmationDialog";
import { CheckCircleIcon } from "lucide-react";
import { RootState, useAppSelector } from "../../../store/store";
import { toast } from "react-toastify";

const defaultHeaders = userManagementTableHeaders.slice(0, 6);

interface User {
	id: string;
	role: { id: string; name: string; description: string };
	userName: string;
	email: string;
	lastActive: string;
	country: string;
	status: { code: string; label: string };
}

const UserManagementPage: React.FC = () => {
	const [isFilterOpen, setIsFilterOpen] = useState(false);
	const [appliedFilters, setAppliedFilters] = useState<{ [key: string]: string }[]>([]);
	const [loading, setLoading] = useState<boolean>(false);
	const [userListData, setUserListData] = useState<User[]>([]);
	const userDetail = useAppSelector((state: RootState) => state?.user?.userDetail,);
	const [userListPageInfo, setUserListPageInfo] = useState<{
		page_details?: {
			total_page: number;
			current_page: number;
			item_per_page: number;
			total_items: number;
		};
	}>({
		page_details: {
			total_page: 0,
			current_page: 1,
			item_per_page: 10,
			total_items: 0,
		}
	});

	const [queryStrings, setQueryString] = useState({
		page: 1,
		// limit: 10,
		pageSize: 10, // Added pageSize property
		type: userDetail?.role?.name === "SuperAdmin" ? "AllOrganization" : "singleOrganization",
		search: "",
		filters: " ",
	});

	const [confirmAction, setConfirmAction] = useState<{
		open: boolean;
		userId: string | null;
		actionType: 'activate' | 'deactivate' | null;
	}>({ open: false, userId: null, actionType: null });

	const fetchUserData = async () => {
		setLoading(true);

		try {
			const params = {
				...queryStrings,
				filters:
					appliedFilters.length > 0 ? JSON.stringify(appliedFilters[0]) : {},
			};
			
			const userValue = await getUser(params);
			
			const formattedData = userValue?.data?.users?.map(
				(item: {
					id?: string;
					user_name?: string;
					email?: string;
					last_active?: string;
					status?: { code: number; label: string };
					country?: string;
					role?: { id: string; name: string; description: string };
				}) => ({
					id: item.id || "-",
					userName: item.user_name || "-",
					email: item.email || "-",
					status:
						typeof item.status === "object"
							? { ...item.status, code: String(item.status?.code || "") }
							: { code: "-", label: "-" },
					role:
						typeof item.role === "object"
							? item.role
							: { id: "N/A", name: "N/A", description: "N/A" },
					country: item.country || "-",
					lastActive: item.last_active
						? new Date(item.last_active).toLocaleDateString("en-GB")
						: "-",
				}),
			);
			setUserListData(formattedData || []);
			setUserListPageInfo({
				page_details: userValue?.data?.page_details || {
					total_page: 0,
					current_page: 1,
					item_per_page: 10,
					total_items: 0,
				}
			});
		} catch (err) {
			console.error("Failed to fetch form data", err);
			setUserListData([]);
			setUserListPageInfo({
				page_details: {
					total_page: 0,
					current_page: 1,
					item_per_page: 10,
					total_items: 0,
				}
			});
		} finally {
			setLoading(false);
		}
	};

	useEffect(() => {
		fetchUserData();
	}, [queryStrings, appliedFilters]);

	const handleSearchChange = (value: string) => {
		setQueryString((prev) => ({
			...prev,
			search: value,
		}));
	};

	const handleFilterApply = (filters: { [key: string]: string }) => {
		setAppliedFilters([filters]);
		setQueryString((prev) => ({ ...prev, page: 1 }));
	};

	const handleOpenConfirm = (userId: string, actionType: 'activate' | 'deactivate') => {
		setConfirmAction({ open: true, userId, actionType });
	};

	const handleCancel = () => {
		setConfirmAction({ open: false, userId: null, actionType: null });
	};

	const handleConfirmAction = async () => {
		if (!confirmAction.userId || !confirmAction.actionType) return;

		try {
			if (confirmAction.actionType === 'deactivate') {
				await deactivateUser(confirmAction.userId);
				toast.success('User deactivated successfully');
			} else if (confirmAction.actionType === 'activate') {
				await activateUser(confirmAction.userId);
				toast.success('User activated successfully');
			}
			fetchUserData();
		} catch (error) {
			console.error(`Failed to ${confirmAction.actionType} user`, error);
			toast.error(`Failed to ${confirmAction.actionType} user`);
		} finally {
			setConfirmAction({ open: false, userId: null, actionType: null });
		}
	};

	return (
		<>
			<UserManagementFilter
				open={isFilterOpen}
				onClose={() => setIsFilterOpen(false)}
				onFilterApply={handleFilterApply}
				initialFilters={appliedFilters[0] || {}}
			/>
			<div className="bg-white p-6 min-h-screen">
				<div className="flex justify-between items-center ">
					<h1 className="text-2xl font-bold text-[#000000]">Users</h1>
				</div>

				<div className={tailwindStyles.tablePageHead}>
					<SearchFilter
						searchQuery={queryStrings.search}
						setSearchQuery={handleSearchChange}
						onFilterClick={() => setIsFilterOpen(true)}
						placeholder="Search by User Email"
					/>

					<TableContainer
						style={{ height: "37rem", overflowX: "auto", width: "100%" }}
						component={Paper}
						className={tailwindStyles.tableContainerBorder}
					>
						<Table>
							<TableHead>
								<TableRow>
									{defaultHeaders.map((header) => (
										<TableCell
											key={header.id}
											 sx={{fontSize: '16px', fontWeight: 'bold' }}
											className="font-semibold text-gray-700 whitespace-nowrap"
										>
											{header.name}
										</TableCell>
									))}
								</TableRow>
							</TableHead>

							<TableBody>
								{loading ? (
									<TableRow>
										<TableCell colSpan={defaultHeaders.length} align="center">
											<div className="flex justify-center items-center h-full">
												<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500" />
											</div>
										</TableCell>
									</TableRow>
								) : userListData.length === 0 ? (
									<TableRow>
										<TableCell colSpan={defaultHeaders.length} align="center">
											<div className="py-6 text-gray-500 text-lg font-medium">
												No data found
											</div>
										</TableCell>
									</TableRow>
								) : (
									userListData.map((user) => (
										<TableRow key={user.id} className="hover:bg-gray-50">
											<TableCell>{user.userName}</TableCell>
											<TableCell>{user.email}</TableCell>
											<TableCell>
												<span
													className={`px-3 py-1 rounded-full font-semibold ${user.status.label === "active"
															? "bg-[#E8F8F3] text-[#10B981]"
															: "bg-[#FFECEB] text-[#FF4136]"
														}`}
												>
													{user.status.label.charAt(0).toUpperCase() +
														user.status.label.slice(1)}
												</span>
											</TableCell>
											<TableCell>{user.country}</TableCell>
											<TableCell>{user.role.name}</TableCell>
											<TableCell>
												<div className="flex items-center space-x-2 cursor-pointer">
													<button
														type="button"
														className={tailwindStyles.approveButton}
														onClick={() => handleOpenConfirm(user.id, 'activate')}
														disabled={user.status.code === "1"} // Disable if already active
													>
														<CheckCircleIcon fontSize="small" />
													</button>
													<button
														onClick={() => handleOpenConfirm(user.id, 'deactivate')}
														type="button"
														className={tailwindStyles.closeIcon}
														disabled={user.status.code === "2"} // Disable if already inactive
													>
														✕
													</button>
												</div>
											</TableCell>
										</TableRow>
									))
								)}
							</TableBody>
						</Table>
					</TableContainer>
					<TablePagination
						queryStrings={queryStrings}
						setQueryString={setQueryString}
						paging={userListPageInfo?.page_details || { total_page: 0, current_page: 1, item_per_page: 10, total_items: 0 }}
					/>
				</div>
			</div>
			<ConfirmationDialog
				open={confirmAction.open && confirmAction.actionType === 'deactivate'}
				title="Deactivate User"
				description="Are you sure you want to deactivate this user?"
				confirmText="Deactivate"
				cancelText="Cancel"
				onConfirm={handleConfirmAction}
				onCancel={handleCancel}
			/>
			<ConfirmationDialog
				open={confirmAction.open && confirmAction.actionType === 'activate'}
				title="Activate User"
				description="Are you sure you want to activate this user?"
				confirmText="Activate"
				cancelText="Cancel"
				onConfirm={handleConfirmAction}
				onCancel={handleCancel}
			/>
		</>
	);
};

export default UserManagementPage;