import Cookie from 'js-cookie';
import { createApiClient } from '../lib/createApiClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_IDENTITY_API_BASE_URL;
if (!API_BASE_URL) {
	throw new Error('NEXT_PUBLIC_IDENTITY_API_BASE_URL is not defined');
}

const datasetApi = createApiClient(API_BASE_URL);

export const requestOtp = async (email: string) => {
	const response = await datasetApi.post('/login', { email });

	const token = response.data.jwtToken;

	Cookie.set('token', token, {
		expires: 1, 
		secure: true,
		sameSite: 'Lax',
	});
	return response;

};

export const verifyOtp = async (payload: { email: string; otp: string }) => {
	const response = await datasetApi.post("/otpverify", payload);

	const token = response.data.jwtToken;

	Cookie.set('token', token, {
		expires: 1,
		secure: true,
		sameSite: 'Lax',
	});
	return response;

};

export const verifyTOtp = async (payload: { email: string; otp: string }) => {
	const response = await datasetApi.post("/totpverify", payload);
	const token = response.data.jwtToken;
	const userId = response.data.user_id; 
	const refreshToken = response.data.refresh_token;

	Cookie.set('token', token);
	Cookie.set('user_id', userId);
	Cookie.set('refresh_token', refreshToken);
	
	return response;

};
export const getOrganizationList = async (param1) => {
	const response = await datasetApi.get(`/searchOrganization?searchTerm=${param1}`);
	return response;
};





