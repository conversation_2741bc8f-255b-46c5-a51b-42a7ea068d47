'use client';

import type React from 'react';
import {
    User<PERSON><PERSON>,
    User<PERSON>heck,
    Sparkles,
    // Database,
} from "lucide-react";
import DashboardCard from '../../../components/common/DashboardCard';
import UserDetailsSection from './OrganizationSection';
import RecentProjectsSection from './RecentProjectSection';
// import FilterPanel from './FilterPanel';
// import ProjectTable from './ProjectTable';
import { getDashboardData } from '../../../services/profileApiService';
import { useEffect, useState } from 'react';
import ActionCard from '../../../components/overview/ActionCard';
import { FolderPlus, FileText, Clock } from 'lucide-react';
import RecentActivityTable from '../../../components/overview/RecentActivityTable';
import type { RootState } from '../../../store/store';
import { useAppSelector } from '../../../store/store';
import { organizationList } from '../../../services/desApiService';
import { getUser } from '../../../services/userService';
import ProjectSummaryCards from '../../../components/overview/ProjectSummaryCards';
import Link from 'next/link';
// import { useRouter } from "next/navigation";
import { messaging } from '../../../firebase';
import { onMessage } from 'firebase/messaging';
import {
    getStatusColor,statusOptions,
} from "../../../constants/fieldtype";

// import { postMessage } from '../../../services/pushNotifications';



interface role {
    description: string;
    id: string;
    name: string;
}


interface Project {
    projectId: string;
    modelName: string;
    version: string;
    type: string;
    specialty: string;
    subSpecialty: string;
    submittedOn: string;
    gpu: string;
    status: string;
    statusColor: string;
    timeCount: number;
    average_rating: number;
    time:string;
}

interface User {
    id: string;
    role: { id: string; name: string; description: string };
    userName: string;
    email: string;
    lastActive: string;
    status: { code: string; label: string };
}

interface DashboradValues {
    users: { last_month: string; this_year: string; total_count: string };
    Project: {
        last_month: string; this_year: string; total_count: string,
        active_count: string; archive_count: string; pending_count: string; rejected_count: string;
    };
    organization: { last_month: string; this_year: string; total_count: string };
}


const OverviewPage: React.FC = () => {

    // const [loading, setLoading] = useState<boolean>(false);
    const [dashboardData, setDashboardData] = useState<DashboradValues | null>(null);
    const role = useAppSelector((state: RootState) => state.user?.userDetail?.role,) as unknown as role;
    const userDetail = useAppSelector((state: RootState) => state?.user?.userDetail,);
    
    
    // const [loading, setLoading] = useState<boolean>(false);
    const [projectListData, setProjectListData] = useState<Project[]>([]);
    const [userListData, setUserListData] = useState<User[]>([]);

    useEffect(() => {
        fetchOrganizationData();
        fetchFormData();
        fetchUserData();
      
        if (messaging) {
          onMessage(messaging, (payload) => {
            console.log('Message received. ', payload);
          });
        } else {
          console.warn('Messaging is not initialized.');
        }
      }, []);
      

    const fetchOrganizationData = async () => {
        // setLoading(true);
        try {
            const res = await getDashboardData();
            
            setDashboardData(res.data);
        } catch (err) {
            console.error("Failed to fetch form data:", err);
        } finally {
            // setLoading(false);
        }
    };

    const fetchFormData = async () => {
        // setLoading(true);
        try {
             
            const params = {
                page: 1,
                pageSize: 10,
                type: 3,
                status: 0 // Replace with a valid number value for status
            };
            const response = await organizationList(params);
            interface FormDtoItem {
                id: string;
                "Project Name"?: string;
                "Base Version"?: string;
                "Model Type"?: string;
                Specialty?: string;
                "Sub Speciality"?: string;
                created_at?: string;
                status?: string;
                "Required CPU"?: string;
                "Pain Points"?: number;
                like_count?: number;
                average_rating?: number;
                updated_at?: string;
            }

            const formattedData = response?.data?.formdtoData?.map(
                (item: FormDtoItem) => ({
                    projectId: item.id,
                    modelName: item["Project Name"] || "N/A",
                    version: item["Base Version"] || "N/A",
                    type: item["Model Type"] || "N/A",
                    gpu: item["Required CPU"] || "N/A",
                    specialty: item.Specialty || "N/A",
                    subSpecialty: item["Sub Speciality"] || "N/A",
                    submittedOn: item.created_at
                        ? new Date(item.created_at).toLocaleDateString("en-GB")
                        : "N/A",
                    status: item.status,
                    statusColor: "bg-gray-200 text-gray-600",
                    timeCount: item.like_count || 0,
                    average_rating: item.average_rating || 0,
                    time: item.created_at
                        ? new Date(item.created_at).toLocaleTimeString("en-US", {
                            hour: "numeric",
                            minute: "2-digit",
                            hour12: true,
                        })
                        : "N/A",

                }),
            );

            setProjectListData(formattedData || []);

        } catch (err) {
            console.error("Failed to fetch form data", err);
            setProjectListData([]);
        } finally {
            // setLoading(false);
        }
    };
    const filteredProjects = projectListData;
   const formattedRecentProjects = filteredProjects.map((project) => {
  // Handle status as string and fallback if status is "0"
  const statusCode = project.status === "0" ? "3" : project.status;

  const statusOption = statusOptions.find(
    (s) => String(s.code) === String(statusCode)
  );

  const statusValue =
    statusOption?.value || (project.status === "0" ? "Created" : "Pending");

  return {
    id: project.projectId,
    name: project.modelName,
    date: project.submittedOn || '',
    Specialty: project.specialty || '',
    subSpecialty: project.subSpecialty || '',
    time: project.time || '',
    status: statusValue, // Just the value like "Created", "Released for Evaluation"
  };
});


    const fetchUserData = async () => {
        // setLoading(true);

        try {
            const params = {
                page: 1,
                pageSize: 10,
                search: "",
                filters: " ",
            };
            const userValue = await getUser(params);
            const formattedData = userValue?.data?.users?.map(
                (item: {
                    id?: string;
                    user_name?: string;
                    email?: string;
                    last_active?: string;
                    status?: { code: number; label: string };
                    // "role"?: {id: string; name: string; description: string};
                }) => ({
                    id: item.id || "",
                    userName: item.user_name || "",
                    email: item.email || "",
                    status:
                        typeof item.status === "object"
                            ? { ...item.status, code: String(item.status?.code || "") }
                            : { code: "", label: "" },
                    // role: typeof item.role === "object" ? item.role : { id: "N/A", name: "N/A", description: "N/A" },
                    lastActive: item.last_active
                        ? new Date(item.last_active).toLocaleDateString("en-GB")
                        : "",
                }),
            );
            setUserListData(formattedData || []);

        } catch (err) {
            console.error("Failed to fetch form data", err);
            setUserListData([]);
        } finally {
            // setLoading(false);
        }
    };


    return (

        <div className="bg-white min-h-screen p-6">
            {/* Header */}
            <div className="pb-6">
                <h1 className="text-2xl font-bold" style={{ color: '#000000' }}>Dashboard</h1>
                <p className="text-gray-500 text-[16px] font-[Open_Sans] mt-2 font-semibold ">  
                    Welcome {userDetail?.full_name ? userDetail.full_name : ""}!
                </p>
            </div>

            {role?.name !== "User" ?
                <div>
                        <div className="grid grid-cols-1  lg:grid-cols-2 xl:grid-cols-3  w-full px-4 mb-6   md:ml-0 gap-4 ">
                        <DashboardCard
                            title="New Users"
                            count={dashboardData?.users?.total_count ? dashboardData?.users.total_count : "0"}
                            lastMonth={dashboardData?.users?.last_month ? dashboardData?.users.last_month : "0"}
                            thisYear={dashboardData?.users?.this_year ? dashboardData?.users.this_year : "0"}
                            icon={<UserPlus className="w-6 h-6 text-orange-500" />}
                        />
                        <DashboardCard
                            title="Active Users"
                            count={dashboardData?.users?.total_count ? dashboardData?.users.total_count : "0"}
                            lastMonth={dashboardData?.users?.last_month ? dashboardData?.users.last_month : "0"}
                            thisYear={dashboardData?.users?.this_year ? dashboardData?.users.this_year : "0"}
                            icon={<UserCheck className="w-6 h-6 text-orange-500" />}
                        />
                        <DashboardCard
                            title="Total Models"
                            count={dashboardData?.Project?.total_count ? dashboardData.Project.total_count : "0"}
                            lastMonth={dashboardData?.Project?.last_month ? dashboardData.Project.last_month : "0"}
                            thisYear={dashboardData?.Project?.this_year ? dashboardData.Project.this_year : "0"}
                            icon={<Sparkles className="w-6 h-6 text-orange-500" />}
                        />
                    </div>
                    {/* Bottom Sections */}
                    <div className="grid grid-cols-1 xs:grid-cols-1 md:grid-cols-1 xl:grid-cols-2 gap-4">
                        {/* Organizations */}

                        <UserDetailsSection users={userListData} />

                        {/* Recent Projects */}

                        <RecentProjectsSection projects={formattedRecentProjects} />

                    </div>
                </div> :


                <div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                        <Link href="/project-dockets/create">
                            <ActionCard
                                title="Create New Project"
                                subtitle="Start a new AI model submission."
                                icon={<FolderPlus className="w-6 h-6 text-orange-600" />}
                                iconBgColor="bg-orange-100 h-25 w-25"
                            />
                        </Link>

                        <Link href="/documentation" passHref>
                            <ActionCard
                                title="Browse Documentation"
                                subtitle="Access guidelines, user manuals, and FAQs."
                                icon={<FileText className="w-6 h-6 text-cyan-600" />}
                                iconBgColor="bg-cyan-100 h-25 w-25"
                            />
                        </Link>

                        <Link href="/project-dockets">
                            <ActionCard
                                title="View Recent Activity"
                                subtitle="See your latest submissions and evaluations."
                                icon={<Clock className="w-6 h-6 text-blue-600" />}
                                iconBgColor="bg-blue-100 h-25 w-25"
                            />
                        </Link>
                    </div>
                    <div className="p-6">
                        <RecentActivityTable activities={formattedRecentProjects} />
                    </div>
                    <div className="p-6">
                        <ProjectSummaryCards data={dashboardData} />
                    </div>
                </div>}


        </div>
        


    );
};

export default OverviewPage;
