'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';

const data = [
  ['AI Cancer Detection', 'SingHealth', 'Active', 'Oncology', 'Lung Cancer', 'Predictive', 35],
  ['Heart Disease Prediction', 'NUH', 'Pending', 'Cardiology', 'Arrhythmia', 'Diagnostic', 50],
  ['Diabetes Risk Assessment', 'CGH', 'Completed', 'Endocrinology', 'Type 2 Diabetes', 'Preventive', 20],
  ['AI Cancer Detection', 'SingHealth', 'Active', 'Oncology', 'Lung Cancer', 'Predictive', 35],
  ['Heart Disease Prediction', 'NUH', 'Pending', 'Cardiology', 'Arrhythmia', 'Diagnostic', 50],
  ['Diabetes Risk Assessment', 'CGH', 'Completed', 'Endocrinology', 'Type 2 Diabetes', 'Preventive', 20],
];

const headers = ['Project', 'Organization', 'Status', 'Speciality', 'Sub-Speciality', 'Model type', 'Users'];

const ProjectTable = () => {
  return (
    <Box className="bg-white shadow-sm rounded-xl border border-gray-200 w-full overflow-x-auto">
      <Box className="min-w-[800px]">
        {/* Table Headers */}
        <Box className="grid grid-cols-7 px-5 py-3 border-b border-[#d7dade]">
          {headers.map((header) => (
          <Typography
          key={header}
          sx={{
            fontSize: "14px",
            fontWeight: 600,
            color: "#475569",
          }}
        >
          {header}
        </Typography>
          ))}
        </Box>

        {/* Table Rows */}
        {data.map((row, rowIdx) => (
          <Box
            key={`row-${rowIdx}-${row[0]}`} // Use row index and first column value for uniqueness
            className={`grid grid-cols-7 items-center px-5 py-4 ${
              rowIdx !== 0 ? 'border-t border-gray-300' : ''
            }`}
          >
            {row.map((cell, cellIdx) => (
              <Typography key={`cell-${rowIdx}-${cellIdx}`} className="text-sm text-gray-700" sx={{ fontSize: '14px', fontFamily: 'Open Sans' }}>
                {cell}
              </Typography>
            ))}
          </Box>
        ))}
      </Box>
    </Box>
  );
};

export default ProjectTable;
