{"compilerOptions": {"target": "ES2017", "lib": ["ES2017", "WebWorker"], "module": "es2022", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./public", "rootDir": "./src/workers", "declaration": false, "sourceMap": false, "removeComments": true, "noEmitOnError": true}, "include": ["src/workers/**/*"], "exclude": ["node_modules", "public", "**/*.test.ts"]}