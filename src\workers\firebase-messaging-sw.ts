/**
 * Firebase Messaging Service Worker (TypeScript)
 * CSP Compliant implementation with security headers
 * Note: Service workers have their own security context
 */

// Type declarations for Firebase in service worker context
import type { FirebaseApp } from 'firebase/app';
import type { Messaging } from 'firebase/messaging';

declare const firebase: {
  initializeApp: (config: object) => FirebaseApp;
  messaging: () => Messaging;
};
// Import ServiceWorkerGlobalScope type from TypeScript lib.dom.d.ts
/// <reference lib="webworker" />
declare const self: ServiceWorkerGlobalScope;

// Firebase configuration interface
interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId: string;
}

// Notification payload interfaces
interface NotificationPayload {
  notification?: {
    title?: string;
    body?: string;
    icon?: string;
    image?: string;
  };
  data?: Record<string, string | number | boolean | null>;
}

interface NotificationOptions {
  body?: string;
  icon?: string;
  badge?: string;
  tag?: string;
  requireInteraction?: boolean;
  silent?: boolean;
  data?: Record<string, unknown>;
}

// Security: Only import scripts from trusted CDN
// These URLs are whitelisted in our CSP policy
importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-messaging-compat.js');

// Security: Validate that Firebase is loaded before initialization
if (typeof firebase === 'undefined') {
  throw new Error('Firebase SDK failed to load');
}

// Firebase configuration
const firebaseConfig: FirebaseConfig = {
  apiKey: "AIzaSyAFXYE-zGKbl131J43TcOW8Kw9j2a56fYM",
  authDomain: "aimx-a7d32.firebaseapp.com",
  projectId: "aimx-a7d32",
  storageBucket: "aimx-a7d32.firebasestorage.app",
  messagingSenderId: "1048421165508",
  appId: "1:1048421165508:web:add7f684b909a6f2645c91",
  measurementId: "G-TVZ8F7KC79"
};

// Initialize Firebase with security considerations
// Configuration is exposed but this is acceptable for client-side Firebase
try {
  firebase.initializeApp(firebaseConfig);
} catch (error) {
  console.error('Failed to initialize Firebase:', error);
  throw error;
}

// Security: Validate messaging service availability
try {
  firebase.messaging();
} catch (error) {
  console.error('Failed to initialize Firebase Messaging:', error);
  throw error;
}

/**
 * Sanitize string input to prevent XSS attacks
 * @param input - Input string to sanitize
 * @returns Sanitized string
 */
function sanitizeString(input: unknown): string {
  if (typeof input !== 'string') return '';
  // Remove potentially dangerous characters and limit length
  return input
    .replace(/[<>"'&]/g, '') // Remove HTML/script injection chars
    .substring(0, 200) // Limit length
    .trim();
}

/**
 * Sanitize and validate URL input
 * @param input - Input URL to validate
 * @returns Validated URL or null if invalid
 */
function sanitizeUrl(input: unknown): string | null {
  if (typeof input !== 'string') return null;
  try {
    const url = new URL(input);
    // Only allow HTTPS URLs from trusted domains
    if (url.protocol === 'https:' && 
        (url.hostname.endsWith('.gstatic.com') || 
         url.hostname.endsWith('.googleapis.com') ||
         url.hostname === 'localhost')) {
      return url.href;
    }
  } catch (error) {
    console.warn('Invalid URL provided:', input);
    console.warn (error);
  }
  return null;
}

/**
 * Validate notification payload structure
 * @param payload - Notification payload to validate
 * @returns True if payload is valid
 */
function isValidPayload(payload: unknown): payload is NotificationPayload {
  return payload !== null && 
         typeof payload === 'object' && 
         payload !== undefined;
}

// Handle background messages with input validation and sanitization
self.addEventListener('push', (event: PushEvent) => {
  const payload: NotificationPayload = event.data?.json() || {};
  try {
    // Security: Validate payload structure
    if (!isValidPayload(payload)) {
      console.warn('Invalid payload received:', payload);
      return;
    }

    // Security: Sanitize notification data
    const notification = payload.notification || {};
    const title = sanitizeString(notification.title) || 'New Notification';
    const body = sanitizeString(notification.body) || '';
    const icon = sanitizeUrl(notification.icon) || '/favicon.ico';

    console.log('[firebase-messaging-sw.ts] Received background message:', {
      title,
      body: body.substring(0, 100) // Log only first 100 chars for security
    });

    // Security: Validate notification options
    const notificationOptions: NotificationOptions = {
      body: body,
      icon: icon,
      badge: '/favicon.ico',
      tag: 'aimx-notification', // Prevent notification spam
      requireInteraction: false,
      silent: false,
      // Security: Prevent arbitrary data injection
      data: {
        timestamp: Date.now(),
        source: 'firebase',
        originalData: payload.data ? JSON.stringify(payload.data).substring(0, 500) : null
      }
    };

    // Show notification with error handling
    self.registration.showNotification(title, notificationOptions)
      .catch((error: Error) => {
        console.error('Failed to show notification:', error);
      });

  } catch (error) {
    console.error('Error processing background message:', error);
  }
});

// Security: Handle service worker errors
self.addEventListener('error', (event: ErrorEvent) => {
  console.error('Service Worker Error:', event.error);
});

// Security: Handle unhandled promise rejections
self.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
  console.error('Service Worker Unhandled Promise Rejection:', event.reason);
  event.preventDefault();
});

// Service worker activation
self.addEventListener('activate', (event: ExtendableEvent) => {
  console.log('Firebase messaging service worker activated');
  event.waitUntil(self.clients.claim());
});

// Service worker installation
self.addEventListener('install', (event: ExtendableEvent) => {
  console.log('Firebase messaging service worker installed');
  event.waitUntil(self.skipWaiting());
});

export {}; // Make this a module
