"use client";

import React, { useState, useEffect } from "react";
import Image from "next/image";
import { <PERSON>ton, Dialog, DialogTitle, DialogContent, TextField, Autocomplete, List, ListItem, Typography } from "@mui/material";
import Link from "next/link";
import peopleImage from "../../assests/Background images/Homepage-Bg.png";
import signinother from "../../assests/signin.png";
import { GraduationCap, FolderGit2, ScanSearch } from "lucide-react";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { useRouter } from "next/navigation";
import { getOrganizationList } from "../../services/apiService";
import debounce from "lodash.debounce";

interface Organization {
    id: string;
    organization_domain: string;
    organization_email: string;
    organization_name: string;
}

export default function JoinOrganizationPage() {
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [organizations, setOrganizations] = useState<Organization[]>([]);
    
    
    const [searchTerm, setSearchTerm] = useState("");

    const fetchOrganizations = async (term: string) => {
        try {
            const response = await getOrganizationList(term);            
            setOrganizations(response?.data || []);
        } catch (error) {
            console.error("Error fetching organizations:", error);
        }
    };

    // Fetch organizations when dialog opens or searchTerm changes
    useEffect(() => {
        if (isDialogOpen) {
            fetchOrganizations(searchTerm);
        }
    }, [isDialogOpen, searchTerm]);

    const handleSearchInputChange = debounce((event: React.ChangeEvent<HTMLInputElement>) => {
        setSearchTerm(event.target.value);
    }, 300);

    const handleDialogOpen = () => {
        setIsDialogOpen(true);
    };

    const handleDialogClose = () => {
        setIsDialogOpen(false);
    };

    const router = useRouter()

    return (
        <div className="min-h-screen grid grid-cols-12 relative">
            <div className="col-span-12 md:col-span-7 relative w-full h-screen">
                <Image src={peopleImage} alt="Signin Background" fill className="object-cover z-0" priority />
            </div>

            <div className="col-span-12 md:col-span-5 relative flex flex-col items-center justify-center bg-[#fdf7f3] p-6 md:p-12 overflow-hidden">
                <Image
                    src={signinother}
                    alt="Form Background"
                    layout="fill"
                    objectFit="cover"
                    className="absolute z-0"
                />
                {/* Back Button */}
          <div className="absolute top-4 right-4 md:top-8 md:right-8 z-10">

                    <Button
                        style={{ background: "white", color: "black" }}
                        variant="contained"
                        startIcon={<ArrowBackIcon />}
                        onClick={() => router.push("/")}
                    >
                        Back
                    </Button>
                </div>

              <h2 className=" text-2xl md:text-3xl font-bold text-gray-800 text-center mb-6 z-10 font-[Open Sans] mt-20 md:mt-6 whitespace-nowrap">

                    Join an organization
                </h2>

                <div className="bg-white p-10 rounded-3xl shadow-2xl w-full max-w-xl z-10">
                    <h6 className="text-xl font-semibold text-gray-700 mb-6 z-10 font-[Open Sans]">
                        Being part of an organization lets you:
                    </h6>
                    <ul className="space-y-4 mb-8 text-gray-700 text-[15px]">
                        <li className="flex items-start gap-3">
                            <GraduationCap className="text-teal-500 mt-1" size={20} />
                            Show your university, lab, or company work to the AiMx community.
                        </li>
                        <li className="flex items-start gap-3">
                            <FolderGit2 className="text-teal-500 mt-1" size={20} />
                            Collaborate on private datasets, models, and spaces.
                        </li>
                        <li className="flex items-start gap-3">
                            <ScanSearch className="text-teal-500 mt-1" size={20} />
                            Lorem ipsum gfgjhm dedew dsccfv vxcgerrg.
                        </li>
                    </ul>

                    <div className="flex flex-col md:flex-row gap-4">
                        <Button
                            fullWidth
                            variant="outlined"
                            onClick={handleDialogOpen}
                            sx={{
                                color: "#14b8a6",
                                borderColor: "#14b8a6",
                                fontWeight: "bold",
                                width: "230px",
                                "&:hover": {
                                    backgroundColor: "#f0fdfa",
                                },
                            }}
                            className="normal-case font-semibold text-transform-none"
                        >
                            Search for an organization
                        </Button>
                        <Link href="/register" passHref>
                            <Button
                                fullWidth
                                variant="contained"
                                sx={{ fontWeight: 'bold' }}
                                className=" text-transform-none normal-case  bg-gradient-to-r from-orange-600 to-orange-400 text-white shadow-md hover:shadow-lg"
                            >
                                Register your organization
                            </Button>
                        </Link>
                    </div>
                </div>
            </div>

            <Dialog
                open={isDialogOpen}
                onClose={handleDialogClose}
                fullWidth
                maxWidth="sm"
                PaperProps={{
                    sx: {
                        height: "70vh",
                        backgroundColor: "#fdf7f3",
                        borderRadius: "16px",
                        boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.1)",
                    },
                }}
            >
                <DialogTitle
                    sx={{
                        display: "flex",
                        justifyContent: "space-between",
                        alignItems: "center",
                        borderBottom: "1px solid #e0e0e0",
                        marginBottom: 2,
                        fontWeight: "bold",
                        fontSize: "20px",
                        color: "#333",
                    }}
                >
                    Search your organization
                    <Button
                        onClick={()=>{handleDialogClose();setSearchTerm(""); setOrganizations([]); }}
                        sx={{
                            minWidth: "auto",
                            padding: 0,
                            color: "#9e9e9e",
                            "&:hover": {
                                color: "#616161",
                            },
                        }}
                    >
                        ✕
                    </Button>
                </DialogTitle>
                <DialogContent>
                    <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: "bold", color: "#333" }}>
                        Select an organization:
                    </Typography>
                    <Autocomplete
                        options={organizations}
                        getOptionLabel={(option) => option.organization_name || ""}
                        // onChange={(event, value) => setSelectedOrganization(value)}
                        filterOptions={(x) => x} // Disable client-side filtering
                        renderInput={(params) => (
                            <TextField
                                {...params}
                                placeholder="Type to search..."
                                onChange={handleSearchInputChange}
                                fullWidth
                                sx={{
                                    backgroundColor: "#fff",
                                    borderRadius: "8px",
                                    "& .MuiOutlinedInput-root": {
                                        "& fieldset": { borderColor: "#e0e0e0" },
                                        "&:hover fieldset": { borderColor: "#14b8a6" },
                                        "&.Mui-focused fieldset": { borderColor: "#14b8a6" },
                                    },
                                }}
                            />
                        )}
                    />
                    <List sx={{ maxHeight: "60vh", overflowY: "auto", mt: 2, backgroundColor: "#fff", borderRadius: "8px", boxShadow: "0px 2px 10px rgba(0, 0, 0, 0.05)" }}>
                        {organizations.length > 0 ? (
                            organizations.map((org) => (
                                <ListItem
                                    key={org.id}
                                    component="button"
                                    onClick={() => {
                                        
                                        setSearchTerm("");
                                        // handleDialogClose();
                                    }}
                                    sx={{ "&:hover": { backgroundColor: "#f0fdfa" } }}
                                >
                                    <Typography sx={{ fontWeight: "bold", color: "#333" }}>
                                        {org.organization_name}
                                    </Typography>
                                </ListItem>
                            ))
                        ) : (
                            <ListItem>
                                <Typography sx={{ color: "#666", textAlign: "center", width: "100%" }}>
                                    No organizations found
                                </Typography>
                            </ListItem>
                        )}
                    </List>
                </DialogContent>
            </Dialog>
        </div>
    );
}
