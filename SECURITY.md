# OWASP Compliant Security Implementation

This document outlines the comprehensive security measures implemented in the AIMX Organization frontend application, following OWASP (Open Web Application Security Project) guidelines.

## 🛡️ Security Headers Implemented

### Content Security Policy (CSP)
- **Purpose**: Primary defense against Cross-Site Scripting (XSS) attacks
- **Implementation**: Environment-specific policies (development vs production)
- **Location**: `src/lib/csp-config.ts`, `next.config.ts`

#### Development CSP
- Allows `unsafe-inline` and `unsafe-eval` for development tools
- Permits localhost connections
- More permissive for debugging

#### Production CSP
- Strict policy with no `unsafe-inline` or `unsafe-eval`
- HTTPS-only resources
- Minimal trusted domains

### Security Headers Applied

| Header | Purpose | Value |
|--------|---------|-------|
| `X-Frame-Options` | Prevent clickjacking | `DENY` |
| `X-Content-Type-Options` | Prevent MIME sniffing | `nosniff` |
| `X-XSS-Protection` | Legacy XSS protection | `1; mode=block` |
| `Referrer-Policy` | Control referrer leakage | `strict-origin-when-cross-origin` |
| `Permissions-Policy` | Control browser features | Restrictive permissions |
| `Strict-Transport-Security` | Force HTTPS (production) | `max-age=31536000; includeSubDomains; preload` |
| `Cross-Origin-Embedder-Policy` | Control embedding | `credentialless` |
| `Cross-Origin-Opener-Policy` | Control window access | `same-origin` |
| `Cross-Origin-Resource-Policy` | Control resource access | `same-origin` |

## 📁 File Structure

```
src/
├── lib/
│   ├── csp-config.ts          # CSP configuration
│   └── security-headers.ts    # Security headers utility
├── middleware.ts              # Enhanced with security headers
├── app/
│   ├── layout.tsx            # Meta security tags
│   └── api/
│       └── security/
│           └── route.ts      # Secure API example
public/
└── firebase-messaging-sw.js  # Secure service worker
```

## 🔧 Implementation Details

### 1. Next.js Configuration (`next.config.ts`)
- Global security headers applied to all routes
- Environment-specific configurations
- Webpack security enhancements

### 2. Middleware (`src/middleware.ts`)
- Request-level security header application
- Secure cookie handling
- API vs web request differentiation

### 3. Layout Security (`src/app/layout.tsx`)
- Meta tag security headers
- Viewport security settings
- DNS prefetch control

### 4. Service Worker Security (`public/firebase-messaging-sw.js`)
- Input validation and sanitization
- URL validation for notifications
- Error handling and logging

## 🚀 Usage

### Environment Variables
```env
NODE_ENV=production  # Enables strict security headers
NEXT_PUBLIC_APP_URL=https://your-domain.com
```

### CSP Customization
```typescript
// src/lib/csp-config.ts
import { getCSPConfig, buildCSPHeader } from './csp-config';

// Get current environment CSP
const csp = getCSPConfig();

// Build CSP header string
const cspHeader = buildCSPHeader(csp);
```

### Adding New Trusted Domains

#### API Domains (Automatic)
API domains are automatically extracted from environment variables:
```env
NEXT_PUBLIC_IDENTITY_API_BASE_URL=http://your-api-server.com:8000/api/v1/identity
NEXT_PUBLIC_DATASET_BASE_URL=http://your-api-server.com:8000/api/v1/dataset
# Add more API URLs as needed
```

#### Manual CSP Customization
```typescript
// In csp-config.ts, add to appropriate arrays:
'script-src': [
  "'self'",
  "https://your-trusted-domain.com"
],
'connect-src': [
  "'self'",
  "https://your-api-domain.com"
]
```

## 🔍 Security Testing

### CSP Validation
```typescript
import { validateCSP } from './lib/security-headers';

const issues = validateCSP(cspHeaderString);
console.log('CSP Issues:', issues);
```

### Browser Testing
1. Open browser developer tools
2. Check Console for CSP violations
3. Verify security headers in Network tab
4. Test with security scanners

## 📋 OWASP Compliance Checklist

- ✅ **A01:2021 – Broken Access Control**: Middleware authentication checks
- ✅ **A02:2021 – Cryptographic Failures**: Secure cookie attributes, HTTPS enforcement
- ✅ **A03:2021 – Injection**: CSP prevents script injection, input validation
- ✅ **A04:2021 – Insecure Design**: Security-by-design architecture
- ✅ **A05:2021 – Security Misconfiguration**: Comprehensive security headers
- ✅ **A06:2021 – Vulnerable Components**: Regular dependency updates
- ✅ **A07:2021 – Identification and Authentication Failures**: Secure session handling
- ✅ **A08:2021 – Software and Data Integrity Failures**: CSP prevents unauthorized scripts
- ✅ **A09:2021 – Security Logging and Monitoring**: Error logging in service worker
- ✅ **A10:2021 – Server-Side Request Forgery**: URL validation in service worker

## 🔄 Maintenance

### Regular Tasks
1. **Update CSP policies** when adding new third-party services
2. **Review security headers** quarterly
3. **Test CSP compliance** after major updates
4. **Monitor CSP violation reports** (if reporting is enabled)
5. **Update trusted domains** as needed

### Security Monitoring
- Monitor browser console for CSP violations
- Use security scanning tools
- Regular penetration testing
- Dependency vulnerability scanning

## 🚨 Incident Response

If security issues are detected:
1. **Immediate**: Disable affected functionality
2. **Short-term**: Apply security patches
3. **Long-term**: Review and strengthen security policies

## 📞 Security Contact

For security-related issues or questions:
- Create an issue in the repository
- Follow responsible disclosure practices
- Include detailed reproduction steps

## 📚 References

- [OWASP Top 10 2021](https://owasp.org/Top10/)
- [OWASP Secure Headers Project](https://owasp.org/www-project-secure-headers/)
- [MDN CSP Documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP)
- [Next.js Security Headers](https://nextjs.org/docs/advanced-features/security-headers)
