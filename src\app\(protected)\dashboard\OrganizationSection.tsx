'use client';

import type React from 'react';
import { Box, Typography, Avatar } from '@mui/material';
// import { Eye } from 'lucide-react';
import { useRouter } from "next/navigation";


interface UserDetailsSectionProps {
  users: {
    id: string;
    userName: string;
    email: string;
    lastActive: string;
    status: { code: string; label: string };
  }[];
}

const UserDetailsSection: React.FC<UserDetailsSectionProps> = ({ users }) => {
  // Filter out empty or invalid users and limit to 3
  const router = useRouter();
  const displayUsers = users
    .filter(user => user.email && user.email.trim() !== '' && user.email.trim() !== '-')
    .slice(0, 3);
    

  return (
    <Box className="bg-white shadow-sm rounded-xl border border-gray-200 w-full overflow-x-auto">
      {/* Header */}
      <Box className="flex justify-between items-center px-5 py-5 border-b border-[#d7dade] min-w-[600px]">
        <Typography sx={{ fontWeight: 'bold', fontSize: "18px" }} className="text-black text-base font-bold whitespace-nowrap">
          New Users
        </Typography>
        <Typography className="text-xs text-[#004fb0] font-bold cursor-pointer whitespace-nowrap" onClick={() => router.push("/user-management")}>
          View All
        </Typography>
      </Box>

      {displayUsers?.length !== 0 ? (
        <>
          {/* Table Headers */}
          <Box className="flex items-center justify-between px-4 py-3 border-b border-[#d7dade] min-w-[600px]">
            {/* Left: User Profile */}
            <Typography sx={{ fontWeight: 'bold', fontSize: "16px" }} className="text-[#161616] font-bold whitespace-nowrap ml-2">
              User Profile
            </Typography>

            {/* Right: Joined At */}
            <Typography sx={{ fontWeight: 'bold', fontSize: "16px" }} className="text-[#161616] font-bold whitespace-nowrap mr-2">
              Status
            </Typography>
          </Box>


          {/* User Rows */}
          <Box className="min-w-[600px]">
            {displayUsers.map((user, i) => (
              <Box
                key={user.id}
                className={`flex items-center justify-between px-4 py-4 gap-x-5 ${i !== 0 ? 'border-t border-gray-100' : ''}`}
              >
                {/* Left: Profile */}
                <Box className="flex items-center gap-3">
                  <Avatar
                    alt={user.userName || user.email}
                    src=""
                    sx={{ width: 40, height: 40, bgcolor: '#FF6900' }}
                  >
                    {(user?.userName || user.email).trim().charAt(0).toUpperCase()}
                  </Avatar>
                  <Box>
                    <Typography sx={{ fontSize: "16px", fontWeight: "600" }} className="text-gray-800 font-inter">
                      {(user?.userName || user.email.split('@')[0]).charAt(0).toUpperCase() + (user?.userName || user.email.split('@')[0]).slice(1)}
                    </Typography>
                    <Typography sx={{ fontSize: "15px" }} className="text-xs text-gray-500 break-all whitespace-nowrap">
                      {user.email}
                    </Typography>
                  </Box>
                </Box>

                {/* Right: Joined At */}
                <Typography sx={{ fontSize: "17px" }} className={`text-sm font-semibold whitespace-nowrap ${user.status?.code === 'active' ? 'text-green-600' : 'text-gray-700'}`}>
                  {user.status?.label.charAt(0).toUpperCase() + user.status?.label.slice(1) || ''}
                </Typography>
              </Box>
            ))}
          </Box>

        </>
      ) : (
        <Box className="grid grid-cols-1 items-center px-5 py-4 min-w-[500px]">
          <p className="text-gray-500 text-center w-full">No User found.</p>
        </Box>
      )}
    </Box>


  );
};

export default UserDetailsSection;