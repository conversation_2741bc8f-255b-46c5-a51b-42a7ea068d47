"use client";

import React from "react";
import { Box, Typography, Chip, Avatar, Tooltip } from "@mui/material";
import { useRouter } from "next/navigation";
import { statusOptions, getStatusColor } from "../../../constants/fieldtype"

interface Project {
  id: string; // Add unique identifier
  name: string;
  status: string;
  date: string;
  time: string;
}
const RecentProjectsSection = ({ projects }: { projects: Project[] }) => {
  const router = useRouter();
  return (
    <Box className="bg-white shadow-sm rounded-xl border border-gray-200 w-full overflow-x-auto sm:w-auto">
      <Box className="flex justify-between items-center  px-5 py-5  border-b border-[#d7dade] min-w-[700px]">
        <Typography
          sx={{ fontWeight: "bold", fontSize: "18px" }}
          className="text-black text-base font-bold whitespace-nowrap"
        >
          Recent Projects
        </Typography>
        <Typography
          className="text-xs text-[#004fb0] font-bold cursor-pointer whitespace-nowrap"
          onClick={() => router.push("/project-dockets")}
        >
          View All
        </Typography>
      </Box>

      {projects?.length !== 0 ? (
        <>
          <Box className="flex justify-between items-center px-4 py-3 border-b border-[#d7dade] min-w-[700px]">
            <Typography
              sx={{ fontWeight: "bold", fontSize: "16px" }}
              className="text-[#161616] text-base font-bold whitespace-nowrap"
            >
              Project Dockets
            </Typography>
            <Typography
              sx={{ fontWeight: "bold", fontSize: "16px" }}
              className="text-[#161616]text-base font-bold whitespace-nowrap pr-8"
            >
              Status
            </Typography>
            <Typography
              sx={{ fontWeight: "bold", fontSize: "16px" }}
              className="text-[#161616]text-base font-bold whitespace-nowrap"
            >
              Uploaded on
            </Typography>
          </Box>

          <Box className="min-w-[700px]">
            {projects.slice(0, 3).map((project, i) => (
              <Box
                key={project.id}
                className={`grid grid-cols-3 items-center px-5 py-5 gap-x-4 ${
                  i !== 0 ? "border-t border-gray-100" : ""
                }`}
              >
                <Box className="flex items-center gap-3">
                  <Avatar
                    alt={project.name}
                    sx={{ bgcolor: "#FF6900", width: 40, height: 40 }}
                  >
                    {(project.name || "N").trim().charAt(0).toUpperCase()}
                  </Avatar>
                   <Tooltip title={project.name} placement="top" arrow>
                  <Typography
                    sx={{ fontSize: "15px", fontWeight: "500",overflow: 'hidden', textOverflow: 'ellipsis' }}
                    className="text-sm text-black font-inter whitespace-nowrap"
                  >
                    {project.name.charAt(0).toUpperCase() +
                      project.name.slice(1)}
                  </Typography>
                  </Tooltip>
                </Box>

                <Box className="flex justify-center">
                  <span className="font-medium px-2 py-1 text-md ml-2 rounded-lg">
                               {(() => {
                                 const statusOption = statusOptions.find(
                                   (opt) => opt.value === project.status
                                 );
                                 const color = getStatusColor(statusOption?.code ?? 3); // default to 3 (Created)
               
                                 return (
                                   <span
                                     className="px-2 py-1 rounded-md text-sm font-medium capitalize"
                                     style={{
                                       backgroundColor: color.bg,
                                       color: color.text,
                                     }}
                                   >
                                     {project.status}
                                   </span>
                                 );
                               })()}
                             </span>
                  
                </Box>

                <Box className="flex flex-col items-end whitespace-nowrap">
                  <Typography
                    sx={{ fontSize: "16px", fontWeight: "600" }}
                    className="text-sm font-semibold text-gray-800 whitespace-nowrap"
                  >
                    {(() => {
                      const [day, month, year] = project.date
                        .split("/")
                        .map(Number);
                      const correctDate = new Date(year, month - 1, day);
                      return correctDate.toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      });
                    })()}
                  </Typography>

                  <Typography
                    sx={{ fontSize: "15px" }}
                    className="text-xs text-gray-500 self-end"
                  >
                    {project.time}
                  </Typography>
                </Box>
              </Box>
            ))}
          </Box>
        </>
      ) : (
        <Box className="grid grid-cols-3 items-center px-5 py-4 min-w-[600px]">
          <p className="text-gray-500 text-center col-span-3 w-full">
            No projects found.
          </p>
        </Box>
      )}
    </Box>
  );
};

export default RecentProjectsSection;
