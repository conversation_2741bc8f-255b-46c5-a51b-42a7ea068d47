import { configureStore, combineReducers } from "@reduxjs/toolkit";
import { persistStore, persistReducer } from "redux-persist";
import storage from "redux-persist/lib/storage";
import userReducer from "./userSlice";
import accessRightsReducer from "./UserPermissionSlice";
import datasetReducer from "./datasetSlice";
import { useDispatch, useSelector } from "react-redux";
import docketDetails from "./docketSlice";

const persistConfig = {
  key: "root",
  storage,
  whitelist: ["user","accessRights"],
};

const rootReducer = combineReducers({
  user: userReducer,
  dataset: datasetReducer,
  accessRights:accessRightsReducer,
  docket:docketDetails
});

const persistedReducer = persistReducer(persistConfig, rootReducer);

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: false,
    }),
});

export const persistor = persistStore(store);
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: typeof useSelector = useSelector;
