"use client";

import type React from "react";
import {
  Drawer,
  Box,
  Typo<PERSON>,
  IconButton,
  List,
  ListItem,
  Button,
  Divider,
  Grid,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { organizationUpdate } from "../../../services/desApiService";
import { toast } from "react-toastify";
import { formatDate } from "../../../constants/fieldtype";

interface Upload {
  name: string;
  url: string;
}

interface OrganizationDetailsDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  fetchFormData: () => void;
  organization: {
    id: string;
    name: string;
    status: number;
    uploads?: Upload[];
    [key: string]: string | number | boolean | Upload[] | undefined;
  };
}

const orderedFields = [
  "Organization Name",
  "Organization Type",
  "Domain URL",
  "Intent of Enrollment",
  "Organisation Address",
  "Country of Registration",
  "Contact Number",
  "User Name",
  "Admin Email Address",
  "Phone Number",
  "Business Registration Number",
  "Tax Identification Number(TIN/VAT/GST)",
];
const labelMap: Record<string, string> = {
  "User Name": "Admin Name",
  "Admin Email Address": "Admin Email",
};



const OrganizationDetailsDrawer: React.FC<OrganizationDetailsDrawerProps> = ({
  isOpen,
  onClose,
  organization,
  fetchFormData,
}) => {
  const formatLabel = (key: string) => {
    return labelMap[key] || key
      .replace(/_/g, " ")
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  const organizationKeys = orderedFields.filter(
    (key) =>
      organization[key] !== undefined &&
      key !== "uploads" &&
      key !== "status" &&
      key !== "id" &&
      key !== "type" &&
      key !== "like_count"
  );


  const handleStatusChange = async (status: string) => {
    try {
      const payload = { id: organization.id, status };
      const response = await organizationUpdate(payload);
      if (response?.data) {
        toast.success(`Status ${payload.status.toLowerCase()} successfully`);
        fetchFormData();
        onClose();
      }
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");
    }
  };

  return (
    <Drawer
      anchor="right"
      open={isOpen}
      onClose={onClose}
      PaperProps={{
        sx: { width: { xs: "100%", md: 700, lg: 700 } },
        className: "bg-white shadow-lg",
      }}
    >
      <Box className="flex items-center justify-between p-4 border-b border-gray-200">
        <Typography sx={{ fontWeight: "700", fontSize: {md:"24px", sm:"20px" }, lineHeight: "33.24px", color: "#000000D9",whiteSpace: "nowrap" }}>
          Organization Details
        </Typography>
        <IconButton onClick={onClose}>
          <CloseIcon />
        </IconButton>
      </Box>

<Box className="p-4 overflow-y-auto sm:overflow-x-hidden overflow-x-auto h-[calc(100vh-70px)]">
  <Box className="min-w-[650px]">
        <Typography
          variant="subtitle1"
          sx={{ fontSize: "18px", fontWeight: 600 }}
        >
          Details:
        </Typography>
        
        <List disablePadding>
          {organizationKeys
            .filter(
              (key) =>
                !Array.isArray(organization[key]) &&
                key !== "Business License" &&
                key !== "Business Registration Certificate"
            )
            .map((key) => (

              <ListItem key={key} disableGutters sx={{ py: 1 ,}}>
                <Grid container alignItems="flex-start"  spacing={1}>
                  <Grid item xs={5} >
                    <Typography
                      variant="subtitle2"
                      sx={{ fontSize: "15px", fontWeight: 500 ,whiteSpace: "nowrap"}}
                    >
                      {formatLabel(key)}:
                    </Typography>
                  </Grid>

                  {/* <Grid item xs={1} textAlign="center">
                    <Typography
                      variant="subtitle2"
                      sx={{ fontSize: "16px", fontWeight: 600 }}
                    >
                      :
                    </Typography>
                  </Grid> */}

                  <Grid item xs={7}>
                    <Typography variant="body2" sx={{
                      fontSize: "15px",
                      marginLeft: {md:"15px", xs:"40px"},

                    }}>
                      {(key === "created_at" || key === "updated_at") &&
                        organization[key]
                        ? formatDate(organization[key] as string)
                        : String(organization[key] || "-")}
                    </Typography>
                  </Grid>
                </Grid>
              </ListItem>
            ))}

          {/* Download Links for Business License and Registration Certificate */}
          {(organization["Business License"] ||
            organization["Business Registration Certificate"]) && (
              <>
                <Typography
                  variant="subtitle2"
                  sx={{ fontSize: "16px", fontWeight: 600, color: "black",py:1 }}
                >
                  Uploads:
                </Typography>
                <ListItem disableGutters sx={{ py: 1 }}>
                  <Grid container spacing={1}>
                    <Grid item xs={12}>
                      {organization["Business Registration Certificate"] && (
                        <Typography
                          component="a"
                          href={`http://13.229.196.7:8000/${organization["Business Registration Certificate"]}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{
                            display: "block",
                            fontSize: "16px",
                            color: "#2563EB",
                            lineHeight: "33.24px",
                            mb: 0.5,
                          }}
                        >
                          Business Registration Certificate
                        </Typography>
                      )}
                      {organization["Business License"] && (
                        <Typography
                          component="a"
                          href={`http://13.229.196.7:8000/${organization["Business License"]}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          sx={{
                            display: "block",
                            fontSize: "16px",
                            color: "#2563EB",
                            lineHeight: "33.24px",
                          }}
                        >
                          Business License
                        </Typography>
                      )}
                    </Grid>
                  </Grid>
                </ListItem>
              </>
            )}
        </List>

        {/* Uploads section */}
        {organizationKeys.some((key) => Array.isArray(organization[key])) && (
          <>
            <Divider sx={{ my: 2 }} />
            <Typography
              variant="subtitle1"
              sx={{ fontSize: "18px", fontWeight: 600, mb: 1, my: 2 }}
            >
              Uploads:
            </Typography>
            <List disablePadding>
              {organizationKeys
                .filter((key) => Array.isArray(organization[key]))
                .map((key) => (
                  <ListItem key={key} disableGutters sx={{ py: 1 }}>
                    <Grid container alignItems="center" spacing={1}>
                      <Grid item xs={5}>
                        <Typography
                          variant="subtitle2"
                          sx={{ fontSize: "16px", fontWeight: 600 }}
                        >
                          {formatLabel(key)}
                        </Typography>
                      </Grid>

                      <Grid item xs={1} textAlign="center">
                        <Typography
                          variant="subtitle2"
                          sx={{ fontSize: "16px", fontWeight: 600 }}
                        >
                          :
                        </Typography>
                      </Grid>

                      <Grid item xs={6}>
                        {Array.isArray(organization[key]) &&
                          organization[key].length > 0 ? (
                          (organization[key] as Upload[]).map((file) => (
                            <Typography
                              key={file.url}
                              component="a"
                              href={file.url}
                              target="_blank"
                              sx={{
                                display: "block",
                                fontSize: "16px",
                                color: "#2563EB",
                                textDecoration: "underline",

                              }}
                            >
                              {file.name}
                            </Typography>
                          ))
                        ) : (
                          <Typography variant="body2" sx={{ fontSize: "16px" }}>
                            No file uploaded
                          </Typography>
                        )}
                      </Grid>
                    </Grid>
                  </ListItem>
                ))}
            </List>
          </>
        )}
      </Box>
      </Box>

      <Box className="flex justify-center p-4 border-t border-gray-200">
        {String(organization?.status) === "0" && (
          <>
            <Button
              variant="contained"
              color="error"
              onClick={() => handleStatusChange("REJECTED")}
              sx={{ mr: 1 }}
            >
              Reject
            </Button>
            <Button
              variant="contained"
              color="success"
              onClick={() => handleStatusChange("APPROVED")}
            >
              Approve
            </Button>
          </>
        )}
      </Box>
    </Drawer>
  );
};

export default OrganizationDetailsDrawer;
