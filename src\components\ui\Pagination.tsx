"use client";

import Pagination from "@mui/material/Pagination";
import { Typography, Select, MenuItem, FormControl } from "@mui/material";
import type { SelectChangeEvent } from "@mui/material";
import type React from "react";
import PropTypes from "prop-types";

interface QueryStrings {
	page: number;
	pageSize: string | number;
}

interface Paging {
	total_page: number;
	current_page: number;
	item_per_page: number;
	total_items: number;
}

interface TablePaginationProps {
	queryStrings: QueryStrings;
	setQueryString: React.Dispatch<React.SetStateAction<QueryStrings>>;
	paging: Paging;
}

const TablePagination: React.FC<TablePaginationProps> = ({
	queryStrings,
	setQueryString,
	paging,
}) => {
	const handlePageSizeChange = (event: SelectChangeEvent<string | number>) => {
		const payload = { ...queryStrings };
		payload.pageSize = event.target.value as string;
		payload.page = 1; // Reset page to 1 when page size changes
		setQueryString(payload);
	};
	const handleChange = (_e: React.ChangeEvent<unknown>, p: number) => {
		const payload = { ...queryStrings };
		payload.page = p;
		setQueryString(payload);
	};

	return (
		<div className="w-full flex flex-col items-center justify-center gap-4 mt-2 md:flex-row md:justify-between md:items-center">

			{/* Rows per page */}
			<div className="flex items-center justify-start gap-2">
				<Typography variant="subtitle2" className="text-sm md:text-base text-[#000000]">
					Rows per page:
				</Typography>
				<FormControl variant="outlined" size="small">
					<Select
						value={queryStrings.pageSize || "100"}
						onChange={handlePageSizeChange}
						displayEmpty
						size="small"
						sx={{
							fontSize: "0.7rem",
							minWidth: "60px",
							height: "30px",
							".MuiSelect-select": {
								padding: "4px 8px",
							},
						}}
						MenuProps={{
							PaperProps: {
								sx: {
									maxHeight: 120,
									fontSize: "0.7rem",
								},
							},
						}}
					>
						<MenuItem value="10">10</MenuItem>
						<MenuItem value="50">50</MenuItem>
						<MenuItem value="All">All</MenuItem>
					</Select>
				</FormControl>
			</div>

			{/* Pagination and Info */}
			<div className="flex flex-col items-center gap-2 md:flex-row md:justify-end md:items-center md:gap-4">

				{(() => {
					// Calculate correct total pages based on current page size
					// Calculate correct total pages based on current page size
					const totalItems = Number(paging?.total_items) || 0;
					let itemsPerPage = Number(queryStrings.pageSize) || Number(paging?.item_per_page) || 10;
					if (queryStrings.pageSize === "All") {
						itemsPerPage = totalItems;
					}

					const totalPages = totalItems > 0 && itemsPerPage > 0
						? Math.ceil(totalItems / itemsPerPage)
						: 0;


					// Ensure current page doesn't exceed total pages
					const currentPage = Math.min(queryStrings.page || 1, totalPages || 1);

				

					return (
						<Pagination
							count={totalPages}
							size="medium"
							page={currentPage}
							variant="outlined"
							shape="rounded"
							onChange={handleChange}
							showFirstButton
							showLastButton
							siblingCount={0}
							boundaryCount={1}
							sx={{
								"& .MuiPaginationItem-root": {
									minWidth: "27px",
									height: "27px",
								},
							}}
						/>
					);
				})()}
				{(() => {
					// Safe parsing of pagination values
					const totalItems = Number(paging?.total_items) || 0;
					const currentPage = Number(paging?.current_page) || Number(queryStrings.page) || 1;
					// Use queryStrings.pageSize as the primary source for items per page
					// Handle "All" option by using total items
					let itemsPerPage = Number(queryStrings.pageSize) || Number(paging?.item_per_page) || 10;
					if (queryStrings.pageSize === "All") {
						itemsPerPage = totalItems;
					}

					if (totalItems > 0 && currentPage > 0 && itemsPerPage > 0) {
						const startItem = ((currentPage - 1) * itemsPerPage) + 1;
						const endItem = Math.min(currentPage * itemsPerPage, totalItems);

						return (
							<Typography variant="subtitle2" className="text-sm md:text-base text-[#000000]">
								Showing {startItem} - {endItem} out of {totalItems}
							</Typography>
						);
					}

					return (
						<Typography variant="subtitle2" className="text-sm md:text-base text-[#000000]">
							Showing 0 - 0 out of 0
						</Typography>
					);
				})()}
			</div>
		</div>
	);
};

TablePagination.propTypes = {
	queryStrings: PropTypes.object.isRequired,
	setQueryString: PropTypes.func.isRequired,
	paging: PropTypes.object,
};

export default TablePagination;
