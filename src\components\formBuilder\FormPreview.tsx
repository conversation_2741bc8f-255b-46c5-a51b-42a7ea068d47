"use client";
import { useState } from "react";
import {
	TextField,
	Radio,
	RadioGroup,
	FormControlLabel,
	FormLabel,
	FormControl,
	Button,
	Card,
	Typography,
	Divider,
	Alert,
	Checkbox,
	Select,
	MenuItem,
	Switch,
	Box,
	Autocomplete,
} from "@mui/material";

import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import type { Dayjs } from "dayjs";
import "./style.css";
type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

interface FormOption {
  id: number | string;
  value: string;
  label?: string;
}

interface Field {
	id: string | number;
	type: FieldType;
	label: string;
	placeholder?: string;
	name?: string;
	required: boolean;
	options?: FormOption[];
	size?: number;
	position?: number;
	section_id?: string | number;
	textfieldtype?: string;
	min?: number;
	max?: number;
	dateFormat?: string;
	mask?: string;
	dropdown_type?: string;
	multiple?: boolean;
	accept?: string;
	fullWidth?: boolean;
	onClick?: (values: FormValues) => void;
	startIcon?: React.ElementType;
	endIcon?: React.ElementType;
}

interface Section {
	id: string | number;
	label: string;
	position?: number;
	is_default?: boolean;
}

interface FormValues {
	[key: string]: string | number | boolean | File | File[] | null | undefined | string[];
}

interface FormErrors {
	[key: string]: string | null;
}
interface FormPreviewProps {
	handlePublish: () => void;
	constructedJSON: {
		sections: Section[];
		fields: Field[];
	};
	id?: number; 
}

const FormPreview: React.FC<FormPreviewProps> = ({ constructedJSON, handlePublish }) => {
	const [formValues, setFormValues] = useState<FormValues>({});
	const [errors, setErrors] = useState<FormErrors>({});
	const [submitSuccess, setSubmitSuccess] = useState<boolean>(false);

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value, files } = e.target;

		if (files) {
			setFormValues({
				...formValues,
				[name]: files.length > 1 ? Array.from(files) : files[0],
			});
		} else {
			setFormValues({
				...formValues,
				[name]: value,
			});
		}

		// Clear error when user types
		if (errors[name]) {
			setErrors({
				...errors,
				[name]: null,
			});
		}
	};

	const renderField = (field: Field) => {
		switch (field.type) {
			case 1: // Text/Number
				return (
					<FormControl
						component="fieldset"
						margin="normal"
						error={!!errors[field.id]}
						fullWidth
					>
						<FormLabel component="legend">
							{field.label}
							{field.required && "*"}
						</FormLabel>
						<TextField
							fullWidth
							name={String(field.id)}
							// label={field.label}
							required={field.required}
							variant="outlined"
							margin="normal"
							value={formValues[field.id] || ""}
							onChange={handleChange}
							error={!!errors[field.id]}
							helperText={errors[field.id]}
							placeholder={field.placeholder}
							type={
								field?.textfieldtype?.toLowerCase() === "number"
									? "number"
									: "text"
							}
							inputProps={{
								min: field.min,
								max: field.max,
							}}
							className="inputfield"
						// style={{background:"#feeadc",padding:"14px 14px"}}
						/>
					</FormControl>
				);
			case 6: // Radio
				return (
					<FormControl
						component="fieldset"
						margin="normal"
						error={!!errors[field.id]}
						fullWidth
					>
						<FormLabel
							component="legend"
							style={{ marginBottom: "18px", color: "#0009" }}
						>
							{field.label}
							{field.required && "*"}
						</FormLabel>
						<RadioGroup
							name={String(field.id)}
							value={formValues[field.id] || ""}
							onChange={handleChange}
							row
						>
							{field.options?.map((option: FormOption) => (
								<FormControlLabel
									key={option.id}
									value={option.value}
									control={<Radio />}
									label={option.value}
								/>
							))}
						</RadioGroup>
					</FormControl>
				);
			case 3: // Select/Multi-select
				return (
					<FormControl fullWidth margin="normal" error={!!errors[field.id]}>
						<FormLabel className="select-label" style={{ paddingBottom: "10px" }} component="legend">
							{field.label}
							{field.required && "*"}
						</FormLabel>

						{field.dropdown_type === "multi-select" ? (
							<Select
								multiple
								name={String(field.id)}
								value={formValues[field.id] || []}
								onChange={handleChange}
								displayEmpty
								renderValue={(selected) =>
									Array.isArray(selected) && selected.length ? selected.join(", ") : <span style={{ color: "#aaa" }}>{field.placeholder || "Select options"}</span>
								}
							>
								{field.options?.map((option) => (
									<MenuItem key={option.id} value={option.value}>
										{option.value}
									</MenuItem>
								))}
							</Select>
						) : (
							<Select
								name={String(field.id)}
								value={formValues[field.id] || ""}
								onChange={handleChange}
								displayEmpty
							>
								<MenuItem disabled value="">
									<span>{field.placeholder || "Select option"}</span>
								</MenuItem>
								{field.options?.map((option) => (
									<MenuItem key={option.id} value={option.value}>
										{option.value}
									</MenuItem>
								))}
							</Select>
						)}
					</FormControl>
				);

			case 5: // Date picker
				return (
					<LocalizationProvider dateAdapter={AdapterDayjs}>
						<FormControl fullWidth margin="normal" error={!!errors[field.id]}>
							<FormLabel style={{ paddingBottom: "15px" }}>
								{field.label}
								{field.required && "*"}
							</FormLabel>
							<DatePicker
								// label={field.label}
								value={
									formValues[field.id]
										? dayjs(String(formValues[field.id]), field.dateFormat)
										: null
								}
								onChange={(date: Dayjs | null) => {
									const dateValue = date ? date.format(field.dateFormat) : null;
									setFormValues({
										...formValues,
										[field.id]: dateValue,
									});
									if (errors[field.id] && dateValue) {
										setErrors({
											...errors,
											[field.id]: null,
										});
									}
								}}
								format={field.dateFormat || "DD-MM-YYYY"}
								// mask={field.mask || "__-__-____"}
								slotProps={{
									textField: {
										fullWidth: true,
										error: !!errors[field.id],
										helperText: errors[field.id],
									},
								}}
							/>
						</FormControl>
					</LocalizationProvider>
				);
			case 8: // Switch (boolean toggle)
				return (
					<FormControl fullWidth margin="normal" error={!!errors[field.id]}>
						<FormControlLabel
							control={
								<Switch
									checked={Boolean(formValues[field.id])}
									onChange={(e) => {
										setFormValues({
											...formValues,
											[field.id]: e.target.checked,
										});
										// Clear error if any
										if (errors[field.id]) {
											setErrors({
												...errors,
												[field.id]: null,
											});
										}
									}}
									color="primary"
									name={String(field.id)}
								/>
							}
							label={
								<div className="flex items-center">
									{field.label}
									{field.required && (
										<span className="text-red-500 ml-1">*</span>
									)}
								</div>
							}
						/>
					</FormControl>
				);
			case 2: // Checkbox
				return (
					<FormControl
						component="fieldset"
						margin="normal"
						error={!!errors[field.id]}
						fullWidth
					>
						<FormLabel component="legend" style={{ color: "#575757", fill: "#F06D1A" }}>
							{field.label}
							{field.required && <span className="text-red-500 ml-1">*</span>}
						</FormLabel>

						<div className="flex flex-col gap-2">
							{field.options?.map((option) => {
								const selectedValues: string[] = Array.isArray(
									formValues[field.id],
								) && Array.isArray(formValues[field.id]) && (formValues[field.id] as unknown[]).every((val) => typeof val === "string")
									? (formValues[field.id] as string[])
									: [];

								const isChecked = selectedValues.includes(option.value);

								return (
									<FormControlLabel
										key={option.id}
										control={
											<Checkbox
												checked={isChecked}

												onChange={(e) => {
													const updatedValues = e.target.checked
														? [...selectedValues, option.value]
														: selectedValues.filter(
															(val) => val !== option.value,
														);

													setFormValues((prev) => ({
														...prev,
														[field.id]: updatedValues,
													}));

													if (errors[field.id]) {
														setErrors((prev) => ({
															...prev,
															[field.id]: null,
														}));
													}
												}}
												name={`${field.id}_${option.id}`}
											/>
										}
										label={option.label || option.value} // <-- Use label if available
									/>
								);
							})}
						</div>

						{errors[field.id] && (
							<Typography variant="caption" color="error" display="block">
								{errors[field.id]}
							</Typography>
						)}
					</FormControl>
				);
			// case 9: // Email
			// 	return (
			// 		<FormControl
			// 			component="fieldset"
			// 			margin="normal"
			// 			error={!!errors[field.id]}
			// 			fullWidth
			// 		>
			// 			<FormLabel component="legend">
			// 				{field.label}
			// 				{field.required && "*"}
			// 			</FormLabel>
			// 			<TextField
			// 				fullWidth
			// 				name={String(field.id)}
			// 				// label={field.label}
			// 				required={field.required}
			// 				variant="outlined"
			// 				margin="normal"
			// 				value={formValues[field.id] || ""}
			// 				onChange={handleChange}
			// 				error={!!errors[field.id]}
			// 				helperText={errors[field.id]}
			// 				placeholder={field.placeholder}
			// 				type="email"
			// 			/>
			// 		</FormControl>
			// 	);
			case 9: // Autocomplete
				return (
					<FormControl fullWidth margin="normal" error={!!errors[field.id]}>
						<FormLabel component="legend" className="mb-2">
							{field.label}
							{field.required && <span className="text-red-500 ml-1">*</span>}
						</FormLabel>
						<Autocomplete
							multiple={field.multiple}
							options={field.options?.map((opt) => opt.value) || []}
							value={
								field.multiple
									? Array.isArray(formValues[field.id])
										? formValues[field.id]
										: []
									: formValues[field.id] || null
							}
							onChange={(_, newValue) => {
								handleChange({
									target: {
										name: field.id.toString(),
										value: newValue,
									},
								} as React.ChangeEvent<HTMLInputElement>);
							}}
							renderInput={(params) => (
								<TextField
									{...params}
									placeholder={field.placeholder || "Start typing..."}
									error={!!errors[field.id]}
									helperText={errors[field.id]}
								/>
							)}
						/>
					</FormControl>
				);
			case 7: // File upload
				return (
					<FormControl fullWidth margin="normal" error={!!errors[field.id]}>
						<FormLabel component="legend" className="mb-2">
							{field.label}
							{field.required && <span className="text-red-500 ml-1">*</span>}
						</FormLabel>

						<Box
							tabIndex={0} 
							sx={{
								width: "100%",
								height: "50px",
								display: "flex",
								justifyContent: "start",
								alignItems: "center",
								padding: "6.5px 14px",
								border: `1px solid ${errors[field.id] ? "#f44336" : "rgba(0, 0, 0, 0.23)"}`,
								borderRadius: "4px",
								fontSize: "0.875rem",
								lineHeight: "1.4375",
								boxSizing: "border-box",
								cursor: "pointer",
								outline: "none",
								"&:focus": {
									border: `2px solid ${errors[field.id] ? "#f44336" : "#1976d2"}`,
								},
							}}
							onClick={() =>
								document.getElementById(`file-upload-${field.id}`)?.click()
							}
						>
							<Box >
								<Typography
									sx={{ fontSize: "0.975rem", lineHeight: "1.4375" }}
									color={errors[field.id] ? "error" : "#adadad"}
									display={"block"}
									alignItems={"start"}
								>
									{field.placeholder ||
										`Click to upload ${field.multiple ? "files" : "a file"}`}
								</Typography>
								<Typography
									sx={{ fontSize: "0.875rem", lineHeight: "1.3" }}
									display={"inline-block"}
									color="#adadad"
								>
									{field.accept
										? `Accepted formats: ${field.accept}`
										: "png, jpg, jpeg, pdf"}
								</Typography>

							</Box>

							<input
								id={`file-upload-${field.id}`}
								type="file"
								name={String(field.id)}
								hidden
								onChange={handleChange}
								accept={field.accept}
								multiple={field.multiple}
								disabled
							/>
						</Box>

						{/* Selected files display */}
						{formValues[field.id] && (
							<Box mt={1}>
								<Typography variant="subtitle2">Selected files:</Typography>
								{field.multiple ? (
									<ul className="list-disc pl-5">
										{(Array.isArray(formValues[field.id])
											? (formValues[field.id] as File[])
											: [formValues[field.id] as File]
										).map((file) => (
											<li key={file.name} className="text-sm">
												{file.name} ({Math.round(file.size / 1024)} KB)
											</li>
										))}
									</ul>
								) : (
									<Typography variant="body2">
										{(formValues[field.id] as File).name} (
										{Math.round((formValues[field.id] as File).size / 1024)} KB)
									</Typography>
								)}
							</Box>
						)}
					</FormControl>

				);
			case 4: // Button
				return (
					<>
						<FormLabel
							component="legend"
							style={{ visibility: "hidden", marginBottom: "15px" }}
						>
							{field.label}
						</FormLabel>
						<Button
							key={field.id}
							variant="contained"
							// color={field.color || "primary"}
							// size={field.size || "medium"}
							fullWidth={field.fullWidth !== false}
							onClick={() => field.onClick(formValues)}
							className="prebutton sm:w-auto p-8 px-6 py-3 bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white"
							// disabled={field.disabled}
							startIcon={field.startIcon && <field.startIcon />}
							endIcon={field.endIcon && <field.endIcon />}
						>
							{field.label}
						</Button>
					</>
				);
			default:
				return (
					<Typography color="error">
						Unknown field type: {field.type}
					</Typography>
				);
		}
	};
	return (
		<div className="min-h-screen flex justify-center p-4 relative d-flex  ">
			{/* Form Container */}
			<div className="w-full max-w-5xl flex flex-col ">
				<Card
					sx={{
						overflow: "auto",
					}}
					className="backdrop-blur-sm bg-white/90 shadow-xl hover:shadow-4xl transition-shadow duration-300"
				>
					{" "}
					{submitSuccess && (
						<Alert
							severity="success"
							className="mb-4"
							onClose={() => setSubmitSuccess(false)}
						>
							Form submitted successfully!
						</Alert>
					)}
					<form>
						{constructedJSON.sections
							.sort(
								(a, b) =>
									((a.position) || 0) - ((b.position) || 0),
							)
							.map((section: Section) => (
								<div key={section.id} className="mb-4">
									<Typography variant="h6" className="mb-4 p-4 ">
										{section.label}
									</Typography>
									<Divider className="mb-4" />

									{/* Grid container with ordered fields */}
									<div
										className="grid w-full pt-4 pl-8 pr-8 gap-4"
										style={{
											display: "grid",
											gridTemplateColumns: "repeat(12, 1fr)",
											gap: "16px",
										}}
									>
										{constructedJSON.fields
											.filter(
												(field: Field) => field.section_id === section.id,
											)
											.sort((a, b) => (a.position || 0) - (b.position || 0))
											.map((field: Field) => {
												// Determine column span - default to 12 if not specified
												const colSpan = field.size || 12;

												// Ensure colSpan is valid (4, 6, or 12)
												const validColSpan = [4, 6, 12].includes(colSpan)
													? colSpan
													: 12;

												return (
													<div
														key={field.id}
														style={{
															gridColumn: `span ${validColSpan} / span ${validColSpan}`,
														}}
													>
														{renderField(field)}
													</div>
												);
											})}
									</div>
								</div>
							))}
					</form>
					<div className="flex justify-center p-4">
						<Button
							variant="contained"
							style={{ lineHeight: "1", width: "100px" }}
							size="medium"
							onClick={handlePublish}
							className="prebutton  bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white text-transform-none"
						>
							Publish
						</Button></div>

				</Card>
			</div>
		</div>
	);
};

export default FormPreview;
