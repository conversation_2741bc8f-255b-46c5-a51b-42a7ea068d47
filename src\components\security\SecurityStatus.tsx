'use client';

import React, { useState, useEffect } from 'react';
import { runSecurityTests, SecurityTestSuite } from '../../lib/security-test';

interface SecurityStatusProps {
  showDetails?: boolean;
}

const SecurityStatus: React.FC<SecurityStatusProps> = ({ showDetails = false }) => {
  const [testResults, setTestResults] = useState<SecurityTestSuite | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Run security tests
    try {
      const results = runSecurityTests();
      setTestResults(results);
    } catch (error) {
      console.error('Failed to run security tests:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  if (isLoading) {
    return (
      <div className="p-4 bg-gray-100 rounded-lg">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-1/4 mb-2"></div>
          <div className="h-3 bg-gray-300 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (!testResults) {
    return (
      <div className="p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
        <h3 className="font-semibold">Security Status Unavailable</h3>
        <p>Unable to load security test results.</p>
      </div>
    );
  }

  const { overall, csp, headers, cookies } = testResults;
  const scorePercentage = Math.round((overall.score / overall.maxScore) * 100);
  
  const getStatusColor = (passed: boolean) => {
    return passed ? 'text-green-600' : 'text-red-600';
  };

  const getStatusIcon = (passed: boolean) => {
    return passed ? '✅' : '❌';
  };

  const getBadgeColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-green-100 text-green-800 border-green-200';
    if (percentage >= 70) return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    return 'bg-red-100 text-red-800 border-red-200';
  };

  return (
    <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Security Status</h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium border ${getBadgeColor(scorePercentage)}`}>
          {scorePercentage}% Secure
        </div>
      </div>

      <div className="mb-4">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm text-gray-600">Overall Score</span>
          <span className={`font-semibold ${getStatusColor(overall.passed)}`}>
            {overall.score}/{overall.maxScore}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className={`h-2 rounded-full ${scorePercentage >= 90 ? 'bg-green-500' : scorePercentage >= 70 ? 'bg-yellow-500' : 'bg-red-500'}`}
            style={{ width: `${scorePercentage}%` }}
          ></div>
        </div>
      </div>

      {showDetails && (
        <div className="space-y-4">
          {/* CSP Tests */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Content Security Policy</h4>
            <div className="space-y-1">
              {csp.map((test, index) => (
                <div key={index} className="flex items-center text-sm">
                  <span className="mr-2">{getStatusIcon(test.passed)}</span>
                  <span className={getStatusColor(test.passed)}>{test.message}</span>
                  <span className="ml-auto text-xs text-gray-500 capitalize">
                    {test.severity}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Security Headers Tests */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Security Headers</h4>
            <div className="space-y-1">
              {headers.map((test, index) => (
                <div key={index} className="flex items-center text-sm">
                  <span className="mr-2">{getStatusIcon(test.passed)}</span>
                  <span className={getStatusColor(test.passed)}>{test.message}</span>
                  <span className="ml-auto text-xs text-gray-500 capitalize">
                    {test.severity}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Cookie Security Tests */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Cookie Security</h4>
            <div className="space-y-1">
              {cookies.map((test, index) => (
                <div key={index} className="flex items-center text-sm">
                  <span className="mr-2">{getStatusIcon(test.passed)}</span>
                  <span className={getStatusColor(test.passed)}>{test.message}</span>
                  <span className="ml-auto text-xs text-gray-500 capitalize">
                    {test.severity}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {!showDetails && (
        <div className="text-center">
          <button 
            onClick={() => window.location.reload()}
            className="text-sm text-blue-600 hover:text-blue-800 underline"
          >
            Refresh Security Status
          </button>
        </div>
      )}

      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          Security tests run automatically. Last updated: {new Date().toLocaleTimeString()}
        </p>
      </div>
    </div>
  );
};

export default SecurityStatus;
