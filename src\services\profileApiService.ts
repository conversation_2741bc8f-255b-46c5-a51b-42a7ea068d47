import { createApiClient } from '../lib/createApiClient';

const PROFILE_BASE_URL = process.env.NEXT_PUBLIC_PROFILE_API_BASE_URL;

const profileApi = createApiClient(PROFILE_BASE_URL);

export const Getrole = async (Userid:string) => {
	const response = await profileApi.get(`/${Userid}`);
	return response;
};

export const updateProfile = async (payload) => {
	const response = await profileApi.put('/',payload);
	return response;
};
export const addGlobalSetting = async (payload) => {
	const response = await profileApi.post('/general-setting',payload);
	return response;
};
export const UpdateGlobalSetting = async (payload) => {
	const response = await profileApi.put('/general-setting',payload);
	return response;
};
export const getGlobalSetting = async () => {
	const response = await profileApi.get('/general-setting');
	return response;
};
export const addOrganizationSetting = async (payload) => {
	const response = await profileApi.post('/org-setting/update',payload);
	return response;
};
export const getOrganizationList = async () => {
	const response = await profileApi.get('/organization/non-singhealth', );
	return response;
};
export const addOrganizationId = async (payload) => {
	const response = await profileApi.post('/org-setting/get', payload);
	return response;
};

export const getDashboardData = async () => {
	const response = await profileApi.get('/overview');
	return response;
};
export const profileImageUpload = async (payload: FormData) => {
	
	return await profileApi.post("/image", payload, {
	  headers: {
		'Content-Type': 'multipart/form-data',
	  },
	});
  };



