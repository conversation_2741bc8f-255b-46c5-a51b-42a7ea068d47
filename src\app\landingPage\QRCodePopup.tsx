"use client";

import {
	<PERSON><PERSON>,
	<PERSON>alogContent,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	Box,
	Button,
	IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import OTPInput from "react-otp-input";
import { useFormik } from "formik";
import * as Yup from "yup";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { verifyTOtp } from "../../services/apiService";
import { toast } from "react-toastify";
import { fetchUserById } from "../../store/userthunk";
import { getPermissionData } from "../../services/rolesapiService";
import { setAccessRightsList } from "../../store/UserPermissionSlice";
import type { ThunkDispatch, UnknownAction } from "@reduxjs/toolkit";
import { useDispatch } from "react-redux";
import { AllMenuItems } from "./paths";
// import type { RootState } from "../../store/store";
// import { useAppSelector } from "../../store/store";

type Props = {
	open: boolean;
	onClose: () => void;
	setShowQRPopup: (show: boolean) => void;
	qrimage: string;
	email: string;
};



function QRCodePopup({ open, onClose, setShowQRPopup, qrimage, email }: Props) {
	
	const router = useRouter();
	const dispatch =
		useDispatch<ThunkDispatch<unknown, unknown, UnknownAction>>();

	// const accessModules = useAppSelector((state: RootState) =>
	// 	state?.accessRights?.accessRightsList?.modules?.map((mod) => mod?.module_name),
	// );

	const formik = useFormik({
		initialValues: { otp: "" },
		validationSchema: Yup.object({
			otp: Yup.string()
				.matches(/^\d{6}$/, "OTP must be exactly 6 digits")
				.required("OTP is required"),
		}),
		onSubmit: async (values) => {
			try {
				const res = await verifyTOtp({
					email: email,
					otp: String(values.otp),
				});

				toast.success(res.data.message || "OTP verified successfully");

				if (res?.data?.user_id && res?.data?.role_id) {
					await dispatch(fetchUserById(res?.data?.user_id));

					const Permission = await getPermissionData(res?.data?.role_id);
					Permission.data.modules.push({
						module_id: "0e882c3f-74da-4386-8633-76f2d7e6cdm6",
						module_name: "profile",
						permissions: [
							{
								permission_id: "5a4b7322-0a86-4f48-b16a-30cdeaf2e649",
								permission_name: "read",
							},
							{
								permission_id: "72d9b3a2-243d-4964-9349-0b26b8624a57",
								permission_name: "update",
							},
						],
					});

					dispatch(setAccessRightsList(Permission.data));

					const updatedAccessModules = Permission.data.modules.map(
						(mod: { module_name: string }) => mod.module_name,
					);
					const filteredMenuItems = AllMenuItems.filter((item) =>
						updatedAccessModules.includes(item.label),
					);

					const defaultModuleName = filteredMenuItems?.[0]?.path;
					router.push(defaultModuleName);
					setShowQRPopup(false);
				} else {
					toast.error("User or role ID missing in response.");
				}
			} catch (error) {
				toast.error(error?.response?.data?.error || "OTP verification failed");
			}
		},
	});

	return (
		<Dialog open={open} onClose={onClose} fullWidth maxWidth="xs">
			<DialogContent sx={{ position: "relative" }}>
				<IconButton
					onClick={onClose}
					sx={{ position: "absolute", top: 8, right: 8 }}
				>
					<CloseIcon />
				</IconButton>

				<Typography variant="h6" fontWeight="bold" textAlign="center">
					Setup Two Factor Authentication
				</Typography>
				<Divider sx={{ my: 2 }} />

				<Typography
					variant="subtitle2"
					sx={{ fontWeight: "bold" }}
					gutterBottom
					textAlign="center"
				>
					To proceed, scan this QR code with the Google Authenticator or Microsoft Authenticator app on your mobile device. 
				</Typography>
				<Box display="flex" justifyContent="center" mb={2}>
					<Image
						src={`data:image/png;base64,${qrimage}`}
						alt="QR Code"
						width={200}
						height={200}
					/>
				</Box>

				<Typography
					variant="subtitle2"
					sx={{ fontWeight: "bold" }}
					gutterBottom
					textAlign="center"
				>
					Enter the OTP from your authenticator app 
				</Typography>

				<form onSubmit={formik.handleSubmit}>
					<Box
						display="flex"
						justifyContent="center"
						flexDirection="column"
						alignItems="center"
					>
						<OTPInput
							value={formik.values.otp}
							onChange={(val) => formik.setFieldValue("otp", val)}
							numInputs={6}
							inputStyle={{
								width: "40px",
								height: "40px",
								margin: "5px",
								fontSize: "18px",
								border: "1px solid #ccc",
								borderRadius: "8px",
								textAlign: "center",
							}}
							renderInput={(props) => (
								<input
									{...props}
									className="otp-input"
									type="tel"
									inputMode="numeric"
								/>
							)}
						/>
						{formik.touched.otp && formik.errors.otp && (
							<Typography color="error" variant="caption" mt={1}>
								{formik.errors.otp}
							</Typography>
						)}
					</Box>

					<Button
						fullWidth
						type="submit"
						variant="contained"
						className="text-transform-none"
						sx={{
							mt: 3,
							py: 1.5,
							fontSize: "18px",
							fontWeight: "bold",
							height: "50px",
							background: "linear-gradient(to right, #FF512F, #F09819)",
							color: "white",
							borderRadius: "6px",
							boxShadow: "0px 4px 10px rgba(255, 81, 47, 0.3)",
							"&:hover": {
								background: "linear-gradient(to right, #F09819, #FF512F)",
							},
						}}
					>
						Verify
					</Button>
				</form>
			</DialogContent>
		</Dialog>
	);
}

export default QRCodePopup;
