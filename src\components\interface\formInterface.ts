// import type { SelectChangeEvent } from "@mui/material";

export type FieldType = 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

export interface FormOption {
  id: number | string;
  value: string;
  label?: string;
}

export interface FormField {
  id: number;
  type: FieldType;
  label: string;
  placeholder?: string;
  name?: string;
  required: boolean;
  options?: FormOption[];
  size?: number;
  position?: number;
  section_id?: string | number;
  textfieldtype?: string;
  min?: number;
  max?: number;
  dateFormat?: string;
  field_type?: string;
  mask?: string;
  multiple?: boolean;
  accept?: string;
  fullWidth?: boolean;
  onClick?: (values: Record<string, unknown>) => void;
  startIcon?: React.ElementType;
  endIcon?: React.ElementType;
}

export interface FormFieldWithValue extends FormField {
  value: string | number | boolean | File | File[] | string[] | null;
}

export interface FormValues {
  fields: FormFieldWithValue[];
}

export interface FormErrors {
  [key: string]: string | null;
}

export interface FormSection {
  id: string | number;
  label: string;
  position?: number;
}


export interface Metadata {
  dataType: string;
  taskType: string;
  modelFramework: string;
  modelArchitecture: string;
  modelWeightUrl: { path: string } | { link: string; pat: string };
  modelDatasetUrl: string;
}
export interface FormConfig {
  sections: FormSection[];
  fields: FormField[];
  type: number;
  metadata: Metadata;
}