"use client";

import React, { Suspense, useMemo } from "react";
import { Box } from "@mui/material";
import GlobalCard from "../../../components/common/GlobalCard";
import OrganisationCard from "../../../components/common/OrganisationCard";
import { Building2, Globe } from "lucide-react";
import { RootState, useAppSelector } from "../../../store/store";

const SettingPage = () => {
  const userDetail = useAppSelector((state: RootState) => state?.user?.userDetail);

  // Determine if user can access global settings
  const canAccessGlobal = useMemo(() => {
    return userDetail?.role?.name === "SuperAdmin" || userDetail?.role?.name === "Collaborator";
  }, [userDetail]);

  // Set default tab based on role
  const [activeSystemTab, setActiveSystemTab] = React.useState(
    canAccessGlobal ? "global" : "organisation"
  );

  return (
    <Box className="p-4 md:p-6 flex flex-col xl:flex-row gap-6">
      <div className="w-full md:w-80 p-4 md:p-6">
        <h2 className="text-lg font-semibold text-gray-700 mb-6">
          System Settings
        </h2>

        {canAccessGlobal && (
          <Box
            onClick={() => setActiveSystemTab("global")}
            className={`cursor-pointer px-4 py-2 rounded-md flex items-center gap-2 ${
              activeSystemTab === "global"
                ? "bg-[#FEF4ED] text-[#F06D1A] font-semibold"
                : "text-gray-500"
            }`}
          >
            <Globe className="w-5 h-5 mr-2" /> Global Settings
          </Box>
        )}

        <Box
          onClick={() => setActiveSystemTab("organisation")}
          className={`cursor-pointer mt-4 px-4 py-2 rounded-md flex items-center gap-2 ${
            activeSystemTab === "organisation"
              ? "bg-[#FEF4ED] text-[#F06D1A] font-semibold"
              : "text-gray-500"
          }`}
        >
          <Building2 className="w-5 h-5 mr-2" />
          Organisation Settings
        </Box>
      </div>

      <div className="flex-1 w-full px-4 md:px-0">
        {activeSystemTab === "global" && canAccessGlobal && <GlobalCard />}
        {activeSystemTab === "organisation" && <OrganisationCard />}
      </div>
    </Box>
  );
};

const SuspendedSettingPage = () => (
  <Suspense fallback={<div>Loading...</div>}>
    <SettingPage />
  </Suspense>
);

export default SuspendedSettingPage;
