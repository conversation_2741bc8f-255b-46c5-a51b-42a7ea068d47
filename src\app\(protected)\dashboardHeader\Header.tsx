"use client";

import { useState, useRef, useEffect } from "react";
import { <PERSON>, User, LogOut, Menu } from "lucide-react";
import AvatarFallback from "../../../components/ui/AvatarFallback";
import NotificationPopup from "../../../components/common/NotificationPopup";
import { useMediaQuery, useTheme } from "@mui/material";
import { useRouter } from "next/navigation";
import type { RootState } from "../../../store/store";
import { useAppDispatch, useAppSelector } from "../../../store/store";
import { clearUserState } from "../../../store/userSlice";
import { clearAccessRightsList } from "../../../store/UserPermissionSlice";
import Link from "next/link";
import { tailwindStyles } from "../../../styles/tailwindStyles";

interface HeaderProps {
	onMenuToggle?: () => void;
}

const Header: React.FC<HeaderProps> = ({ onMenuToggle }) => {
	const dispatch = useAppDispatch();
	const userDetail = useAppSelector(
		(state: RootState) => state?.user?.userDetail,
	);


	const theme = useTheme();
	const router = useRouter();
	const isMobile = useMediaQuery(theme.breakpoints.down("sm"));

	const [notificationCount, setNotificationCount] = useState<number>(0);
	const [showPopup, setShowPopup] = useState<boolean>(false);
	const [showDropdown, setShowDropdown] = useState<boolean>(false);

	const dropdownRef = useRef<HTMLDivElement | null>(null);
	const notificationRef = useRef<HTMLDivElement | null>(null);

	useEffect(() => {
		router.prefetch("/profile");
	}, [router]);

	const handleBellClick = () => {
		const newShowPopup = !showPopup;
		setShowPopup(newShowPopup);
		if (newShowPopup) setNotificationCount(3);
	};

	const handlePopupClose = () => {
		setShowPopup(false);
		setNotificationCount(0);
	};

	const handleLogout = () => {
		document.cookie = "token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/";
		document.cookie = "user_id=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/";
		document.cookie = "refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/";
		dispatch(clearUserState());
		dispatch(clearAccessRightsList());
		window.location.href = "/";
	};

	const handleProfileClick = () => {
		setShowDropdown(false);
	};

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				dropdownRef.current &&
				!dropdownRef.current.contains(event.target as Node)
			) {
				setShowDropdown(false);
			}
			if (
				notificationRef.current &&
				!notificationRef.current.contains(event.target as Node)
			) {
				setShowPopup(false);
			}
		};

		document?.addEventListener("mousedown", handleClickOutside);
		return () => document.removeEventListener("mousedown", handleClickOutside);
	}, []);

	return (
		<header
			className={`flex items-center justify-between bg-white shadow-sm ${isMobile ? "p-3" : "p-4"}`}
		>
			{isMobile && (
				<button
					type="button"
					onClick={onMenuToggle}
					className="p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-gray-300"
				>
					<Menu className="w-6 h-6 text-[#7C8CA1]" />
				</button>
			)}

			<div className="flex-1" />
			<div className="flex items-center gap-4 relative">
				<div className="relative">
					{/* <Settings className="w-5 h-5 text-[#7C8CA1] cursor-pointer hover:text-gray-600" /> */}
				</div>
				<div
					className="relative"
					style={{ height: "20px" }}
					ref={notificationRef}
				>
					<button
						type="button"
						onClick={handleBellClick}
						className="relative focus:outline-none"
					>
						{/* <Bell className="w-5 h-5 text-[#7C8CA1] hover:text-gray-600" /> */}
						{notificationCount > 0 && (
							<span className="absolute -top-1 -right-1 w-4 h-4 text-xs text-white flex items-center justify-center rounded-full bg-red-500">
								{notificationCount}
							</span>
						)}
					</button>
					{showPopup && (
						<div
							className={`absolute right-0 mt-2 z-50 ${isMobile ? "w-[280px]" : "w-[320px]"}`}
						>
							<NotificationPopup
								count={notificationCount}
								isOpen={showPopup}
								onClose={handlePopupClose}
							/>
						</div>
					)}
				</div>

				<div className="relative" ref={dropdownRef}>
					<button
						type="button"
						onClick={() => setShowDropdown((prev) => !prev)}
						className="focus:outline-none"
					>
						{userDetail?.user_profile_img_path ? (
							<img src={`data:image/png;base64,${userDetail.user_profile_img_path}`} alt="User Avatar" className="w-8 h-8 md:w-9 md:h-9 rounded-full border border-gray-300 object-contain cursor-pointer" />
						) : (
							<AvatarFallback name={userDetail?.full_name || userDetail?.email} className="w-8 h-8 md:w-9 md:h-9  mb-2 rounded-full border border-gray-300 cursor-pointer" />
						)}
					</button>
					{showDropdown && (
						<div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg z-50 border border-gray-200">
							<Link href="/profile" prefetch>
								<button
									type="button"
									onClick={handleProfileClick}
									className={tailwindStyles.profileButton}
								>
									<User className="w-4 h-4" />
									Profile
								</button></Link>
							<div className="border-t border-gray-200" />
							<button
								type="button"
								onClick={handleLogout}
								className="flex items-center gap-2 w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 cursor-pointer"
							>
								<LogOut className="w-4 h-4" />
								Logout
							</button>
						</div>
					)}
				</div>
			</div>
		</header>
	);
};

export default Header;
