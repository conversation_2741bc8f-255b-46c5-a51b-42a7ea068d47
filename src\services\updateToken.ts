import { getMessaging, getToken, isSupported } from "firebase/messaging";
import { app } from "../firebase";
import { createApiClient } from "../lib/createApiClient";
import Cookie from "js-cookie";

const REQUEST_UPD_URL = process.env.NEXT_PUBLIC_REQUEST_UPD_URL;
const updApi = createApiClient(REQUEST_UPD_URL);

export const updateFcmToken = async () => {
  if (typeof window === "undefined" || typeof Notification === "undefined") {
    console.warn("updateFcmToken can only run in the browser.");
    return;
  }

  const messagingSupported = await isSupported();
  if (!messagingSupported) {
    console.warn("Firebase messaging is not supported in this browser.");
    return;
  }

  try {
    const messaging = getMessaging(app);

    const permission = await Notification.requestPermission();
    if (permission !== "granted") {
      console.warn("Notification permission not granted.");
      return;
    }

    const fcmToken = await getToken(messaging, {
      vapidKey: "BAHChF0Rm4IptIOCDb7_rgjD72JLjRBORY1lEwFVbAl2yx1pLEVKyjisnER9y9ho9ijTn763ETTInTThBj9dBWg",
    });

    if (!fcmToken) {
      console.warn("No FCM token retrieved.");
      return;
    }

    const userId = Cookie.get("user_id");
    if (!userId) {
      console.warn("User ID not found in cookies.");
      return;
    }

    const response = await updApi.post("/update-token", {
      user_id: userId,
      firebase_token: fcmToken,
    });

    
    return response;
  } catch (error: unknown) {
    if (error instanceof Error) {
      console.error("Error posting FCM token:", error.message);
    } else {
      console.error("Unknown error posting FCM token:", error);
    }
  }
};
