import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { getConnectSrcDirective } from "./lib/api-domains";

const protectedRoutes = [
    "/dashboard",
    "/organizations",
    "/project-dockets",
    "/datasets",
    "/user-management",
    "/audit-logs",
    "/forms",
    "/request-approval",
    "/role-access",
    "/settings",
];

export function middleware(request: NextRequest) {
    const token = request.cookies.get("token")?.value || null;
    const { pathname } = request.nextUrl;

    // Handle authentication redirects
    if ((!token || token === "undefined") && protectedRoutes.some((route) => pathname?.startsWith(route))) {
        const response = NextResponse.redirect(new URL("/", request.url));
        applySecurityHeadersToResponse(response, request);
        return response;
    }

    if (token && pathname === "/") {
        const response = NextResponse.redirect(new URL("/organizations", request.url));
        applySecurityHeadersToResponse(response, request);
        return response;
    }

    // Create response and apply security headers
    const response = NextResponse.next();
    applySecurityHeadersToResponse(response, request);

    return response;
}

/**
 * Get security headers based on environment and request type
 */
function getSecurityHeaders(isAPIRequest: boolean = false) {
    const isProduction = process.env.NODE_ENV === 'production';

    if (isAPIRequest) {
        return {
            'Content-Security-Policy': "default-src 'none'",
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'no-referrer',
            'Cross-Origin-Resource-Policy': 'same-origin',
            'Cache-Control': 'no-store, no-cache, must-revalidate',
            'Pragma': 'no-cache',
            'Expires': '0'
        };
    }

    // Web page security headers
    const cspDirectives = [
        "default-src 'self'",
        isProduction
            ? "script-src 'self' https://www.gstatic.com https://apis.google.com https://www.google.com"
            : "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://apis.google.com https://www.google.com localhost:* 127.0.0.1:*",
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://www.gstatic.com",
        "font-src 'self' https://fonts.gstatic.com data:",
        isProduction
            ? "img-src 'self' data: blob: https:"
            : "img-src 'self' data: blob: https: http: localhost:* 127.0.0.1:*",
        `connect-src ${getConnectSrcDirective(isProduction).join(' ')}`,
        "frame-src 'self' https://www.google.com https://www.facebook.com",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "frame-ancestors 'none'"
    ];

    if (isProduction) {
        cspDirectives.push("upgrade-insecure-requests");
    }

    return {
        'Content-Security-Policy': cspDirectives.join('; '),
        'X-Frame-Options': 'DENY',
        'X-Content-Type-Options': 'nosniff',
        'X-XSS-Protection': '1; mode=block',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
        ...(isProduction && {
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
        }),
        'Cross-Origin-Embedder-Policy': 'credentialless',
        'Cross-Origin-Opener-Policy': 'same-origin',
        'Cross-Origin-Resource-Policy': 'same-origin'
    };
}

/**
 * Apply appropriate security headers based on request type
 */
function applySecurityHeadersToResponse(response: NextResponse, request: NextRequest) {
    const { pathname } = request.nextUrl;

    // Determine if this is an API request
    const isAPIRequest = pathname.startsWith('/api/');

    // Get appropriate security headers
    const securityHeaders = getSecurityHeaders(isAPIRequest);

    // Apply security headers
    Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
    });

    // Additional security measures

    // Remove server information
    response.headers.delete('Server');
    response.headers.delete('X-Powered-By');

    // Set secure cookie attributes for authentication cookies
    if (response.headers.get('Set-Cookie')) {
        const cookies = response.headers.get('Set-Cookie')?.split(',') || [];
        const secureCookies = cookies.map(cookie => {
            if (cookie.includes('token') || cookie.includes('session')) {
                // Add security attributes to authentication cookies
                let secureCookie = cookie;
                if (!secureCookie.includes('HttpOnly')) {
                    secureCookie += '; HttpOnly';
                }
                if (!secureCookie.includes('Secure') && process.env.NODE_ENV === 'production') {
                    secureCookie += '; Secure';
                }
                if (!secureCookie.includes('SameSite')) {
                    secureCookie += '; SameSite=Strict';
                }
                return secureCookie;
            }
            return cookie;
        });
        response.headers.set('Set-Cookie', secureCookies.join(','));
    }
}

export const config = {
    matcher: [
        "/",
        "/dashboard",
        "/organizations",
        "/project-dockets",
        "/datasets",
        "/user-management",
        "/audit-logs",
        "/forms",
        "/request-approval",
        "/role-access",
        "/settings",
    ],
};

