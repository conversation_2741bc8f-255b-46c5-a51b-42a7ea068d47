{"name": "aimx-user", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "react-scripts start --host 0.0.0.0", "lint": "next lint", "security:test": "ts-node scripts/test-security.ts", "security:test:js": "node scripts/test-security.js", "security:test:prod": "NODE_ENV=production ts-node scripts/test-security.ts", "security:audit": "npm audit --audit-level=moderate", "security:check": "npm run security:test && npm run security:audit", "build:sw": "tsc --project tsconfig.sw.json", "prebuild": "npm run build:sw", "type-check": "tsc --noEmit"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/open-sans": "^5.2.6", "@mui/icons-material": "^6.4.8", "@mui/lab": "^6.0.0-beta.31", "@mui/material": "^6.4.8", "@mui/system": "^7.1.1", "@mui/x-data-grid": "^7.28.1", "@mui/x-date-pickers": "^7.28.0", "@reduxjs/toolkit": "^2.6.1", "axios": "^1.8.4", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "firebase": "^11.8.0", "formik": "^2.4.6", "jose": "^6.0.10", "js-cookie": "^3.0.5", "lightningcss": "^1.30.1", "lodash.debounce": "^4.0.8", "lucide-react": "^0.483.0", "next": "15.2.3", "next-auth": "^4.24.11", "papaparse": "^5.5.2", "qs": "^6.14.0", "react": "^19.0.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-otp-input": "^3.1.1", "react-redux": "^9.2.0", "react-toastify": "^11.0.5", "redux-persist": "^6.0.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/papaparse": "^5.3.16", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.2.3", "postcss": "^8.5.3", "tailwindcss": "^4.0.17", "ts-node": "^10.9.2", "typescript": "^5.8.2"}}