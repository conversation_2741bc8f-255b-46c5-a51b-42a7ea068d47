// src/app/layout.tsx
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from "next/font/google";
import "./globals.css";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { AppProviders } from "./providers";
import "@fontsource/open-sans"; // defaults to weight 400


const geistSans = Geist({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-geist-sans",
});

const geistMono = Geist_Mono({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-geist-mono",
});

export const metadata: Metadata = {
  title: "Aimx Organization",
  description: "Aimx Organization",
  // Security-related metadata
  robots: "noindex, nofollow", // Prevent indexing of sensitive application
  referrer: "strict-origin-when-cross-origin",
  // Additional security metadata
  other: {
    "X-UA-Compatible": "IE=edge",
    "format-detection": "telephone=no",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        {/* Security Meta Tags */}
        <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
        <meta httpEquiv="X-Frame-Options" content="DENY" />
        <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
        <meta name="referrer" content="strict-origin-when-cross-origin" />
        <meta httpEquiv="Permissions-Policy" content="camera=(), microphone=(), geolocation=(), payment=()" />

        {/* Prevent automatic phone number detection */}
        <meta name="format-detection" content="telephone=no" />

        {/* DNS Prefetch Control */}
        <meta httpEquiv="x-dns-prefetch-control" content="off" />

        {/* Disable automatic translation */}
        <meta name="google" content="notranslate" />

        {/* Viewport with security considerations */}
        <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, user-scalable=no" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AppProviders>
          {children}
          <ToastContainer
            position="top-right"
            autoClose={3000}
            hideProgressBar={false}
            newestOnTop={false}
            closeOnClick
            rtl={false}
            pauseOnFocusLoss
            draggable
            pauseOnHover
            toastClassName="toast-container"
          />
        </AppProviders>
      </body>
    </html>
  );
}
