// Import the functions you need from the SDKs you need
import { initializeApp, FirebaseApp } from "firebase/app";
import { getMessaging, getToken, isSupported, Messaging } from "firebase/messaging";

// Firebase configuration interface
interface FirebaseConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  measurementId: string;
}

// Your web app's Firebase configuration
const firebaseConfig: FirebaseConfig = {
  apiKey: "AIzaSyAFXYE-zGKbl131J43TcOW8Kw9j2a56fYM",
  authDomain: "aimx-a7d32.firebaseapp.com",
  projectId: "aimx-a7d32",
  storageBucket: "aimx-a7d32.firebasestorage.app",
  messagingSenderId: "1048421165508",
  appId: "1:1048421165508:web:add7f684b909a6f2645c91",
  measurementId: "G-TVZ8F7KC79",
};

// Initialize Firebase
export const app: FirebaseApp = initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging only if supported
export let messaging: Messaging | null = null;

// Initialize messaging asynchronously
const initializeMessaging = async (): Promise<void> => {
  if (typeof window !== "undefined" && await isSupported()) {
    messaging = getMessaging(app);
  }
};

// Call initialization
initializeMessaging().catch(console.error);

/**
 * Generate FCM token for push notifications
 * @returns Promise<string | undefined> - FCM token or undefined if failed
 */
export const generateToken = async (): Promise<string | undefined> => {
  if (!messaging) {
    console.warn("Firebase Messaging is not supported in this environment.");
    return undefined;
  }

  try {
    const token = await getToken(messaging, {
      vapidKey: "BAHChF0Rm4IptIOCDb7_rgjD72JLjRBORY1lEwFVbAl2yx1pLEVKyjisnER9y9ho9ijTn763ETTInTThBj9dBWg",
    });
    
    if (token) {
      console.log("FCM Token generated successfully");
      return token;
    } else {
      console.warn("No registration token available.");
      return undefined;
    }
  } catch (error) {
    console.error("Error generating FCM token:", error);
    return undefined;
  }
};

/**
 * Check if Firebase Messaging is available
 * @returns boolean - true if messaging is available
 */
export const isMessagingAvailable = (): boolean => {
  return messaging !== null;
};

export default app;
