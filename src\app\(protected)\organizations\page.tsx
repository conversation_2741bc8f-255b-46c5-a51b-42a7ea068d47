"use client";

import type React from "react";
import { useState, useEffect } from "react";
import {
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	Paper,
	Button,
} from "@mui/material";
import AttachmentOutlinedIcon from "@mui/icons-material/AttachmentOutlined";
import OrganizationDetailsDrawer from "./OrganizationDetailsDrawer";
import { tailwindStyles } from "../../../styles/tailwindStyles";
import SearchFilter from "../../../components/ui/SearchFilter";
import FilterSidebar from "../../../components/ui/FilterSidebar";
import TablePagination from "../../../components/ui/Pagination";
import {
	deactivateOrganization,
	organizationList,
} from "../../../services/desApiService";
import {
	getStatusClass,
	getStatusLabel,
	formatDate,
} from "../../../constants/fieldtype";
import HeaderSelectionDrawer from "../../../components/common/columDrawer";
import ConfirmationDialog from "../../../components/common/ConfirmationDialog";
import { toast } from "react-toastify";


type Organization = {
	id: string;
	name: string;
	organization_name: string;
	organization_id: string;
	email: string;
	lastActive: string;
	status: number;
	createdAt: string;
	type: string;
};

type TableHeader = {
	id: string;
	name: string;
	defaultVisible?: boolean;
	render?: (
		value: string | number | null,
		row: Organization,
	) => React.ReactNode;
};

interface Paging {
	total_page: number;
	current_page: number;
	item_per_page: number;
	total_items: number;
}

const OrganizationPage: React.FC = () => {
	const [searchQuery, setSearchQuery] = useState("");
	const [drawerOpen, setDrawerOpen] = useState(false);
	const [isFilterOpen, setIsFilterOpen] = useState(false);
	const [columnDrawerOpen, setColumnDrawerOpen] = useState(false);
	const [visibleExtraFields, setVisibleExtraFields] = useState<string[]>([]);
	const [appliedFilters, setAppliedFilters] = useState<
		{ fields: string; value: string }[]
	>([]);
	const [confirmState, setConfirmState] = useState<{
		open: boolean;
		userId: string | null;
		status?: "DEACTIVATED"
	}>({ open: false, userId: null });
	const [selectedOrganization, setSelectedOrganization] =
		useState<Organization | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [organizationListData, setOrganizationListData] = useState<
		Organization[]
	>([]);
	const [organization, setOrganization] = useState<{
		pagingInfo?: Paging;
		formdtoData?: Organization[];
	}>({});
	const [queryStrings, setQueryString] = useState({
		page: 1,
		pageSize: 10, 
		type: 1,
		formname: "",

	});

	useEffect(() => {
		fetchFormData();
	}, [queryStrings, appliedFilters]);

	const fetchFormData = async () => {
	setIsLoading(true);
try {
		const params= {
			...queryStrings,
			filter:
				appliedFilters.length > 0
					? JSON.stringify(appliedFilters)
					: undefined,
		};


		const desformvalue = await organizationList(params);
		setOrganizationListData(desformvalue?.data?.formdtoData || []);
		setOrganization(desformvalue?.data || {});
	}
 catch (err) {
		console.error("Failed to fetch form data", err);
		setOrganizationListData([]);
		setOrganization({});
	} finally {
		setIsLoading(false);
	}
};

	const handleSearch = (query: string) => {
		setSearchQuery(query);
		setQueryString((prev) => ({
			...prev,
			page: 1,
			formname: query,
		}));
	};
	const generateHeaders = (): TableHeader[] => {
		if (
			!Array.isArray(organizationListData) ||
			organizationListData.length === 0
		)
			return [];
		const sampleUser = organizationListData[0];
		const allKeys = Object.keys(sampleUser).filter((key) => {
			const lowerKey = key.toLowerCase();
			return (
				!lowerKey.includes("business license") &&
				!lowerKey.includes("business registration certificate") &&
				!lowerKey.includes("updated_at") &&
				!lowerKey.includes("id") &&
				!lowerKey.includes("type")
			);
		});
		const defaultVisibleKeys = [
			"Organization Name",
			"Organization Type",
			"Admin Name",
			"Admin Email Address",
			"status",
		];

		const orderedKeys = [
			...defaultVisibleKeys,
			...allKeys.filter((key) => !defaultVisibleKeys.includes(key)),
		];

		return orderedKeys.map((key) => ({
			id: key,
			name: key
				.split("_")
				.map((word) => word.charAt(0).toUpperCase() + word.slice(1))
				.join(" "),
			defaultVisible: defaultVisibleKeys.includes(key),
		}));
	};

	const allHeaders = generateHeaders();
	const defaultVisibleHeaders = allHeaders.filter(
		(header) => header.defaultVisible,
	);
	const optionalHeaders = allHeaders.filter((header) => !header.defaultVisible);

	const handleFilterApply = (
		appliedFilters: { fields: string; value: string }[],
	) => {
		setAppliedFilters(appliedFilters);
		setQueryString((prev) => ({ ...prev, page: 1 }));
	};

	const defaultRenderer = (
		value: string | number | null | undefined,
	): string | number => value || "-";

	const fieldRenderers: Record<
		string,
		(
			value: string | number | null | undefined,
			row: Organization,
		) => React.ReactNode
	> = {
		status: (value) => (
			<span
				className={`px-3 py-1 rounded-full font-semibold font-['Open Sans'] ${getStatusClass(Number(value))}`}
			>
				{getStatusLabel(Number(value))}
			</span>
		),
		created_at: (value) => <span>{formatDate(value as string)}</span>,

		attachments: (_value, row) => (
			<AttachmentOutlinedIcon
				sx={{ color: "#91B0F4", fontSize: 24, cursor: "pointer" }}
				onClick={() => {
					setSelectedOrganization(row);
					setDrawerOpen(true);
				}}
			/>
		),
	};
	const handleConfirmDeactivate = async () => {
		if (!confirmState.userId || !confirmState.status) return;
		try {
			await deactivateOrganization(confirmState.userId, confirmState.status);
			toast.success("Organization deactivated successfully");
			fetchFormData();
		} catch (error) {
			console.error("Failed to deactivate user", error);
			toast.error("Failed to deactivate organization");
		} finally {
			setConfirmState({ open: false, userId: null });
		}
	};
	const handleOpenConfirm = (userId: string) => {
		setConfirmState({ open: true, userId, status: "DEACTIVATED" });
	};
	const handleCancel = () => {
		setConfirmState({ open: false, userId: null });
	};
	return (
		<>
			<div className="bg-white p-6 min-h-screen" >
				<FilterSidebar
					type={Number(organizationListData[0]?.type)}
					open={isFilterOpen}
					onFilterApply={handleFilterApply}
					onClose={() => setIsFilterOpen(false)}
					initialFilters={appliedFilters}
				/>

				<div className="flex justify-between items-center gap-x-2">
					<h1 className="text-lg sm:text-2xl font-bold text-[#000000] whitespace-nowrap">Organizations</h1>
					<Button
						variant="outlined"
						size="small"
						onClick={() => setColumnDrawerOpen(true)}
						className="whitespace-nowrap"
					>
						Manage Columns
					</Button>
				</div>

				<div className={tailwindStyles.tablePageHead}>
					<SearchFilter
						searchQuery={searchQuery}
						setSearchQuery={handleSearch}
						onFilterClick={() => setIsFilterOpen(true)}
						placeholder="Search by Organization Name"
					/>

					<TableContainer
						component={Paper}
						elevation={0}
						style={{ height: "37rem", overflowX: "auto", width: "100%" }}
						className="mt-4 border border-gray-300 rounded-md shadow-sm"
					>
						<Table>
							<TableHead
								sx={{
									position: "sticky",
									top: 0,
									backgroundColor: "white",
									zIndex: 1,
									whiteSpace:"nowrap"
								}}
							>
								<TableRow>
									{defaultVisibleHeaders.map((header) => (
										<TableCell
											key={header.id}
											sx={{
												fontWeight: "bold",
												fontSize: "16px",
												fontFamily: "'Open Sans',",
												color: "#475569",
												whiteSpace: "nowrap",
												minWidth: 120,
											}}
										>
											{header.name}
										</TableCell>
									))}
									{visibleExtraFields.map((fieldId) => {
										const header = allHeaders.find((h) => h.id === fieldId);
										return header ? (
											<TableCell
												key={header.id}
												sx={{
													fontWeight: "bold",
													fontSize: "16px",
													fontFamily: "'Open Sans',",
													color: "#475569",
													whiteSpace: "nowrap",
												}}
											>
												{header.name}
											</TableCell>
										) : null;
									})}
									<TableCell
										sx={{
											fontWeight: "bold",
											fontSize: "16px",
											fontFamily: "'Open Sans',",
											color: "#475569",
											whiteSpace: "nowrap",
										}}
									>
										Organization Details
									</TableCell>
									<TableCell
										sx={{
											fontWeight: "bold",
											fontSize: "16px",
											fontFamily: "'Open Sans',",
											color: "#475569",
										}}
									>
										Action
									</TableCell>
								</TableRow>
							</TableHead>

							<TableBody>
								{isLoading ? (
									<TableRow>
										<TableCell
											colSpan={
												defaultVisibleHeaders.length +
												visibleExtraFields.length +
												2
											}
											align="center"
											sx={{ height: "28rem"}}
										>
											<div className="flex justify-center items-center h-full">
												<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500" />
											</div>
										</TableCell>
									</TableRow>
								) : organizationListData.length === 0 ? (
									<TableRow>
										<TableCell
											colSpan={
												defaultVisibleHeaders.length +
												visibleExtraFields.length +
												2
											}
											align="center"
											sx={{ height: "28rem" }}
									
										>
											No records found
										</TableCell>
									</TableRow>
								) : (
									organizationListData.map((user, idx) => (
										<TableRow
											key={user.id}
											className="hover:bg-gray-50"
											sx={{ minWidth: 180 ,whiteSpace:"nowrap" }}
										>
											{defaultVisibleHeaders.map((header) => (
												<TableCell key={`${idx}-${header.id}`}>
													{fieldRenderers[header.id]
														? fieldRenderers[header.id](user[header.id], user)
														: defaultRenderer(user[header.id])}
												</TableCell>
											))}
											{visibleExtraFields.map((fieldId) => (
												<TableCell
													key={`${idx}-${fieldId}`}
													sx={{ minWidth: 180 }}
												>
													{fieldRenderers[fieldId]
														? fieldRenderers[fieldId](user[fieldId], user)
														: defaultRenderer(user[fieldId])}
												</TableCell>
											))}
											<TableCell align="center" >
												<AttachmentOutlinedIcon
													sx={{
														color: "#91B0F4",
														fontSize: 24,
														cursor: "pointer",

													}}
													onClick={() => {
														setSelectedOrganization(user);
														setDrawerOpen(true);
													}}
												/>
											</TableCell>

											<TableCell>
												<button
													type="button"
													onClick={() =>
														handleOpenConfirm(user.organization_id)
													}
													className={
														user.status === 1
															? tailwindStyles.closeIcon
															: tailwindStyles.disabledCloseIcon
													}
													disabled={user.status !== 1}
												>
													✕
												</button>
											</TableCell>
										</TableRow>
									))
								)}
							</TableBody>
						</Table>
					</TableContainer>
					{!isLoading && organizationListData.length > 0 && (
						<TablePagination
							queryStrings={queryStrings}
							setQueryString={setQueryString}
							paging={organization?.pagingInfo || { total_page: 0, current_page: 0, item_per_page: 0, total_items: 0 }}
						/>
					)}
				</div>
			</div>

			<HeaderSelectionDrawer
				open={columnDrawerOpen}
				onClose={() => setColumnDrawerOpen(false)}
				optionalHeaders={optionalHeaders}
				selectedFields={visibleExtraFields}
				setSelectedFields={setVisibleExtraFields}
			/>

			{selectedOrganization && (
				<OrganizationDetailsDrawer
					isOpen={drawerOpen}
					onClose={() => {
						setDrawerOpen(false);
						setSelectedOrganization(null);
					}}
					fetchFormData={fetchFormData}
					organization={selectedOrganization}
				/>
			)}

			<ConfirmationDialog
				open={confirmState.open}
				title="Deactivate User"
				description="Are you sure you want to deactivate this organization?"
				confirmText="Deactivate"
				cancelText="Cancel"
				onConfirm={handleConfirmDeactivate}
				onCancel={handleCancel}
			/>
		</>
	);
};

export default OrganizationPage;
