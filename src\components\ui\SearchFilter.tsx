"use client";

import type React from "react";
import { Search } from "lucide-react";
import { Tune } from "@mui/icons-material";
import { tailwindStyles } from "../../styles/tailwindStyles";

interface SearchFilterProps {
  searchQuery: string;
  setSearchQuery: (value: string) => void;
  onFilterClick?: () => void;
  placeholder?: string;
  hideFilterButton?: boolean; // New prop to hide the button
}

const SearchFilter: React.FC<SearchFilterProps> = ({
  searchQuery,
  setSearchQuery,
  onFilterClick,
  placeholder,
  hideFilterButton, // Destructure the new prop
}) => {
  return (
    <div className="grid grid-cols-12 gap-4 mb-4 items-center">
      
      <div className={tailwindStyles.searchiconDiv}>
        <Search className="text-gray-400 mr-2" size={18} />
        <input
          type="text"
          placeholder={placeholder || "Search..."}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full outline-none bg-transparent"
          style={{ color: "#000000" }}
        />
      </div>
      {!hideFilterButton && onFilterClick && ( // Conditionally render the button
        <button
          type="button"
          onClick={onFilterClick}
          className={`${tailwindStyles.allFilterButton} flex items-center justify-center lg:justify-between px-2 lg:px-4 py-2`}
        >
          <span className="hidden lg:inline text-sm md:text-base lg:text-[16px]">All Filters</span>
          <span className="lg:ml-2">
            <Tune fontSize="small" />
          </span>
        </button>
      )}
    </div>
  );
};

export default SearchFilter;