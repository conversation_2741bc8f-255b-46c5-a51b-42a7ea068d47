"use client";

import type React from "react";
import {
  Mo<PERSON>,
  Box,
  Typography,
  TextField,
  Button,
  IconButton,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import { useState } from "react";
import { UpdateRequest } from "../../services/requestapiService";
import { toast } from "react-toastify";

interface ReqData {
  id: number | string;
  organization_name: string;
  request_type_label: string;
  request_type: string;
  value: string;
  commands_by_organization: string;
}

interface ApprovalModalProps {
  isOpen: boolean;
  onClose: () => void;
  reqData?: ReqData;
  fetchRequests: () => void;
  actiontype?: string;
}

const ApprovalModal: React.FC<ApprovalModalProps> = ({
  isOpen,
  onClose,
  reqData,
  fetchRequests,
  actiontype,
}) => {

  
  const [newcomment, setNewComment] = useState<string>("");
  const handleUpdate = async (status: number) => {
    try {
      const requestData = {
        id: reqData?.id,
        status: status,
        comments_by_singadmin: newcomment,
      };

      const response = await UpdateRequest(requestData);
      if (response?.status === 200) {
        toast.success("Request updated successfully");
        onClose();
        fetchRequests();
        
      } else {
        toast.error("Failed to update request");
      }
    } catch (error) {
      toast.error("Something went wrong");
      console.error(error);
    } finally {
      setNewComment("");
    }
  };

  return (
    <Modal open={isOpen} onClose={onClose} aria-labelledby="approval-modal-title">
      <Box
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
        bg-white rounded-lg shadow-xl p-6 w-[500px] max-w-full"
      >
        <div className="flex justify-between items-center pb-3">
          <Typography
            id="approval-modal-title"
            variant="h6"
            className="font-bold text-md font-['Open_Sans']"
          >
            Approval Request to SingHealth
          </Typography>
          <IconButton onClick={onClose} className="text-gray-500 hover:text-red-500">
            <CloseIcon />
          </IconButton>
        </div>

        <div className="mt-4 space-y-4">
          <div>
            <Typography className="text-sm font-semibold text-gray-600 mb-1">
              Organization Name
            </Typography>
            <TextField
              disabled
              fullWidth
              value={reqData?.organization_name || ""}
              variant="outlined"
              size="small"
              className="bg-[#FEF4ED] rounded-md"
            />
          </div>

          <div>
            <Typography className="text-sm font-semibold text-gray-600 mb-1">
              Request Type
            </Typography>
            <TextField
              disabled
              fullWidth
              variant="outlined"
              size="small"
              value={reqData?.request_type_label || ""}
              className="bg-[#FEF4ED] rounded-md"
            />
          </div>
          <div>
            <Typography className="text-sm font-semibold text-gray-600 mb-1">
              Value
            </Typography>
            <TextField
              disabled
              fullWidth
              value={reqData?.value || ""}
              variant="outlined"
              size="small"
              className="bg-[#FEF4ED] rounded-md"
            />
          </div>
          <div>
            <Typography className="text-sm font-semibold text-gray-600 mb-1">
              Comments
            </Typography>
            <TextField
              multiline
              rows={3}
              value={reqData?.commands_by_organization || ""}
              disabled
              fullWidth
              variant="outlined"
              size="small"
              className="bg-[#FEF4ED] rounded-md"
            />
          </div>

          <div>
            <Typography className="text-sm font-semibold text-gray-600 mb-1">
              Add Comment
            </Typography>
            <TextField
              placeholder="Type your comment..."
              fullWidth
              variant="outlined"
              value={newcomment}
              onChange={(e) => setNewComment(e.target.value)}
              size="small"
              className="bg-[#FEF4ED] rounded-md"
            />
          </div>
        </div>

        <div className="flex justify-center items-center mt-6 gap-4">
         {actiontype==="reject" &&( <Button
            variant="contained"
            className="text-transform-none"
            sx={{
              backgroundColor: "#DC2626",
              "&:hover": { backgroundColor: "#B91C1C" },
              fontFamily: "Open Sans",
              fontWeight: "bold",
              
            }}
            onClick={() => handleUpdate(3)}
          >
            Reject
          </Button>)}
          {actiontype==="approve" &&(<Button
            variant="contained"
            className="text-transform-none"
            sx={{
              backgroundColor: "#16A34A",
              "&:hover": { backgroundColor: "#15803D" },
              fontFamily: "Open Sans",
              fontWeight: "bold",
              
            }}
            onClick={() => handleUpdate(2)}
          >
            Approve
          </Button>)}
        </div>
      </Box>
    </Modal>
  );
};

export default ApprovalModal;
