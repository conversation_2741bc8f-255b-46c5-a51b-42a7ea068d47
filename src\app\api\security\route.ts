/**
 * Security API Route
 * Demonstrates OWASP compliant API security headers
 */

import { NextRequest, NextResponse } from 'next/server';
import { getAPISecurityHeaders } from '../../../lib/security-headers';

export async function GET() {
  try {
    // Security: Validate request origin if needed
    // const origin = request.headers.get('origin');
    // const allowedOrigins = [
    //   process.env.NEXT_PUBLIC_APP_URL,
    //   'https://localhost:3000',
    //   'http://localhost:3000'
    // ].filter(Boolean);

    // Security response data
    const securityInfo = {
      status: 'secure',
      timestamp: new Date().toISOString(),
      csp: 'enabled',
      headers: 'applied',
      environment: process.env.NODE_ENV || 'development'
    };

    // Create response with security headers
    const response = NextResponse.json(securityInfo, { status: 200 });
    
    // Apply API security headers
    const securityHeaders = getAPISecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    console.error('Security API Error:', error);
    
    // Security: Don't expose internal error details
    const errorResponse = NextResponse.json(
      { 
        error: 'Internal server error',
        timestamp: new Date().toISOString()
      }, 
      { status: 500 }
    );

    // Apply security headers even to error responses
    const securityHeaders = getAPISecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      errorResponse.headers.set(key, value);
    });

    return errorResponse;
  }
}

export async function POST(request: NextRequest) {
  try {
    // Security: Validate Content-Type
    const contentType = request.headers.get('content-type');
    if (!contentType?.includes('application/json')) {
      return NextResponse.json(
        { error: 'Invalid content type' },
        { status: 400 }
      );
    }

    // Security: Limit request body size (Next.js handles this by default)
    const body = await request.json();
    
    // Security: Validate request body
    if (!body || typeof body !== 'object') {
      return NextResponse.json(
        { error: 'Invalid request body' },
        { status: 400 }
      );
    }

    // Process security-related request
    const response = NextResponse.json(
      { 
        message: 'Security check completed',
        timestamp: new Date().toISOString()
      },
      { status: 200 }
    );

    // Apply security headers
    const securityHeaders = getAPISecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      response.headers.set(key, value);
    });

    return response;

  } catch (error) {
    console.error('Security API POST Error:', error);
    
    const errorResponse = NextResponse.json(
      { 
        error: 'Bad request',
        timestamp: new Date().toISOString()
      },
      { status: 400 }
    );

    // Apply security headers to error response
    const securityHeaders = getAPISecurityHeaders();
    Object.entries(securityHeaders).forEach(([key, value]) => {
      errorResponse.headers.set(key, value);
    });

    return errorResponse;
  }
}

// Security: Explicitly handle unsupported methods
export async function PUT() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function DELETE() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}

export async function PATCH() {
  return NextResponse.json(
    { error: 'Method not allowed' },
    { status: 405 }
  );
}
