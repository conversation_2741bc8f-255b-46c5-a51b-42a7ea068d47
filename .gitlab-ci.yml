stages:
  - build
  - deploy

variables:
  DOCKER_IMAGE: nagagogulan/aimx-frontend-org

build:
  stage: build
  tags:
    - docker
  image: node:latest  # Using node:latest for the build environment
  before_script:
    # Install Docker inside the container
    - apt-get update && apt-get install -y docker.io
    - export DOCKER_HOST="unix:///var/run/docker.sock"
  script:
    - echo "Building Docker image for the app"
    - docker build -t $DOCKER_IMAGE:latest .
    - echo "$DOCKER_PASSWORD" | docker login -u "$DOCKER_USERNAME" --password-stdin
    - docker push $DOCKER_IMAGE:latest
  only:
    - feature/base_setup  # Or your desired branch

deploy:
  stage: deploy
  tags:
    - docker
  image: node:latest
  before_script:
    - apt-get update && apt-get install -y openssh-client docker.io
    - eval $(ssh-agent -s)
    - echo "$EC2_SSH_KEY" | tr -d '\r' | ssh-add -
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - ssh-keyscan -H "$EC2_HOST" >> ~/.ssh/known_hosts
  script:
    - >
      ssh -o StrictHostKeyChecking=no $EC2_USER@$EC2_HOST "
      echo '$DOCKER_PASSWORD' | docker login --username '$DOCKER_USERNAME' --password-stdin &&
      docker pull $DOCKER_IMAGE:latest &&
      (docker stop app || true) &&
      (docker rm app || true) &&
      docker run -d --name app -p 3000:3000 $DOCKER_IMAGE:latest
      "
  only:
    - feature/base_setup  # Or your desired branch
