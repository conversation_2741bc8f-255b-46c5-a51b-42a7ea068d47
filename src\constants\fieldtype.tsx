export interface FieldType {
    id: number;
    label: string;
  }
  
  export const FieldTypes: FieldType[] = [
    { id: 1, label: "Text Field" },
    { id: 2, label: "Checkbox" },
    { id: 3, label: "Select" },
    { id: 4, label: "Button" },
    { id: 5, label: "Date" },
    { id: 6, label: "Radio" },
    { id: 7, label: "File Upload" },
    { id: 8, label: "Toggle" },
    { id: 9, label: "AutoComplete" },
  ];
  
  export const getFieldTypeName = (type: number): string => {
    const match = FieldTypes.find((ft) => ft.id === type);
    return match ? match.label : "Unknown";
  };

  export const getStatusClass = (value: number) => {
    if (value === 1) return 'bg-[#E8F8F3] text-[#10B981]';
    if (value === 0) return 'bg-[#FFF4E5] text-[#F97316]';
    return 'bg-[#FFECEB] text-[#FF4136]';
  };
  
  export const getStatusLabel = (value: number) => {    
    if (value === 1) return 'Approved';
    if (value === 0) return 'Pending';
    if (value === 10) return 'Deactivated';

    return 'Rejected';
  };
  export const formatDate = (dateStr: string): string => {
    if (!dateStr) return "-";
    const date = new Date(dateStr);
    return date.toLocaleString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  export	const statusOptions = [
		{ code: 3, label: "CREATED" ,value: "Created"},
		{ code: 4, label: "RELEASED_FOR_EVALUATION", value:"Released for Evaluation" },
		{ code: 7, label: "READY_FOR_REVIEW",value:"Ready for Review" },
		{ code: 5, label: "ARCHIVED",value:"Archived" },
		{ code: 6, label: "SET_FOR_DELETION",value:"Set for Deletion" },
		{ code: 8, label: "RESUBMITTED_FOR_EVALUATION",value:"Resubmitted for Evaluation" },
    { code: 9, label: "ADHOC_REQUEST",value:"Adhoc Request" },
    // { code: 0, label: "PENDING", value:"Pending" }
	];

export const getStatusColor = (code: string | number) => {
  const normalizedCode = Number(code) === 0 ? 3 : Number(code); // treat 0 as 3
	const styleMap: Record<number, { bg: string; text: string }> = {
    
		3: { bg: "#f3f4f6", text: "#1f2937" }, //Created
		4: { bg: "#cce3f7", text: "#0074d9" },
		7: { bg: "#e6f4eb", text: "#1e9e4f" },
		5: { bg: "#f2e6f4", text: "#7e0190" },
		6: { bg: "#ffe7e6", text: "#ff1002" },
		8: { bg: "#f0e6e6", text: "#6b0709" },
    9: { bg: "#fff3cd", text: "#856404" }, // ADhoc Request
   // 3: { bg: "#E8F8F3", text: "#10B981" },

	};
	return styleMap[normalizedCode] || { bg: "#f3f4f6", text: "#1f2937"}; // default gray
};

export const superadmin = ["SuperAdmin", "superadmin"];
export const admin = ["Admin", "admin"];
export const user = ["User", "user"];
export const collaborator = ["Collaborator", "collaborator"];

  