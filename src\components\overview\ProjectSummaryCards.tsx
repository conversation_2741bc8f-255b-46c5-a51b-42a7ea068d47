"use client";

import { Card, CardContent, Typography, Box } from "@mui/material";
import { CalendarDays, Clock, Archive, Sparkles } from "lucide-react";

interface ProjectData {
  active_count: string;
  archive_count: string;
  pending_count: string;
  rejected_count: string;
}

const ProjectSummaryCards = ({ data }: { data: { project?: ProjectData } | null }) => {
  const project = data?.project;

  const summaryData = [
    {
      count: project?.active_count || "0",
      label: "Active Projects",
      icon: <Sparkles className="w-8 h-8 text-orange-600" />,
    },
    {
      count: project?.archive_count || "0",
      label: "Archived Projects",
      icon: <Archive className="w-8 h-8 text-orange-600" />,
    },
    {
      count: project?.pending_count || "0",
      label: "Pending Evaluations",
      icon: <Clock className="w-8 h-8 text-orange-600" />,
    },
    {
      count: project?.rejected_count || "0",
      label: "Upcoming Deadlines",
      icon: <CalendarDays className="w-8 h-8 text-orange-600" />,
    },
  ];

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 mt-6">
     
    </div>
  );
};

export default ProjectSummaryCards;
