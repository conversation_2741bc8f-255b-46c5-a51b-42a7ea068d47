'use client';

import React from 'react';
import { Box, Typography } from '@mui/material';
import { useTheme } from '@mui/material/styles';


interface DashboardCardProps {
    title: string;
    count: number | string;
    lastMonth: number | string;
    thisYear: number | string;
    icon: React.ReactNode;
}

const DashboardCard: React.FC<DashboardCardProps> = ({
    title,
    count,
    lastMonth,
    thisYear,
    icon,
}) => {
    const theme = useTheme();

    return (
        <Box
            className="relative bg-white rounded-xl shadow-md overflow-hidden w-full max-w-full md:max-w-lg p-4 sm:p-5 transition duration-300 transform hover:scale-102 hover:shadow-xl ml-1 md:ml-0 hover:shadow-orange-200"
            sx={{ fontFamily: '"Open Sans", ' + theme.typography.fontFamily }}
        >
            {/* Text Section */}
            <Typography
                sx={{ fontWeight: 'bold', fontSize: '35px', fontFamily: '"Open Sans", ' + theme.typography.fontFamily }}
                variant="h5"
                className="text-[#004fb0] font-bold text-lg sm:text-xl"
            >
                {count}
            </Typography>
            <Typography
                sx={{
                    fontWeight: 'bold',
                    fontSize: { xs: '19px', sm: '20px' },
                    color: '#000000',
                    mt: 1,
                    fontFamily: '"Open Sans", ' + theme.typography.fontFamily,
                }}
                variant="h2"
            >
                {title}
            </Typography>

            {/* Last Month / This Year Section */}
            <Box className="mt-6 text-sm sm:text-base text-gray-500 font-['Open_Sans'] flex justify-between w-full">
                <Box className="flex flex-col text-left w-[50%]">
                    <span className="block text-base">Last Month</span>
                    <span className="text-[#16AFB5] mt-1 font-bold block text-xl">{lastMonth}</span>
                </Box>
                <Box className="flex flex-col text-right w-[50%]">
                    <span className="block text-base">This Year</span>
                    <span className="text-[#16AFB5] mt-1 font-bold block text-xl">{thisYear}</span>
                </Box>
            </Box>

            {/* Orange Circle & Icon Overlay */}
            <Box className="absolute top-0 right-0 w-[120px] h-[90px] sm:w-[140px] sm:h-[110px] bg-[#FFEADF] rounded-bl-[100px] z-0" />
            <Box className="absolute top-[30px] right-[40px] sm:top-[40px] sm:right-[50px] text-orange-500 text-[50px] sm:text-[60px]">
                {icon}
            </Box>
        </Box>
    );
};

export default DashboardCard;

                