import {
	Box,
	Button,
	Tabs,
	Tab,
	Typography,
	TextField,
	Radio,
	RadioGroup,
	FormControlLabel,
	Drawer,
} from "@mui/material";
import { useState } from "react";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import Image from "next/image";
import signinother from "../../../../assests/filuploadbg.png";
import { docketUpload } from "../../../../services/uploadApiService";
// import { CheckBox } from "@mui/icons-material";

interface UploadDrawerFormProps {
	setOpen: (open: boolean) => void;
	onFileUpload: (data: { file_path: string; id: string }) => void;
	onContinue: (modelWeightUrl: { link: string; pat: string; type: string }) => void;
}

type TabChangeEvent = React.SyntheticEvent & {
	target: EventTarget & {
		value: number;
	};
};

export default function UploadDrawerForm({
	setOpen,
	onFileUpload,
	onContinue
}: UploadDrawerFormProps) {
	const [loading, setLoading] = useState(false);
	const [activeTab, setActiveTab] = useState<number>(0);
	const [selectedRepo, setSelectedRepo] = useState<string>("github");
	const [link, setLink] = useState<string>("");
	const [pat, setPat] = useState<string>("");
	const [uploadedFiles, setUploadedFiles] = useState<
		Record<string, { file_path: string; id: string }>
	>({});

	const handleCloseDrawer = (): void => {
		setOpen(false);
		setActiveTab(0);
		setSelectedRepo("github");
		setLink("");
		setPat("");
	};

	const handleTabChange = (event: TabChangeEvent, newValue: number): void =>
		setActiveTab(newValue);

	const handleFileUpload = async (file: File) => {
		const fieldId = "upload_file";

		if (uploadedFiles[fieldId]) {
			await handleFileDelete(fieldId);
		}

		try {
			const formData = new FormData();
			formData.append("uploadFile", file);

			setLoading(true);
			const uploadRes = await docketUpload(formData);
			setLoading(false);
			const fullPath = uploadRes?.data?.file_path || file.name;
			const fileId = uploadRes?.data?.id;
			const filePath = fullPath.replace(/^Documents\//, "");

			const fileObj = { file_path: filePath, id: fileId };

			setUploadedFiles((prev) => ({
				...prev,
				[fieldId]: fileObj,
			}));

			onFileUpload(fileObj);
		} catch (error) {
			console.error("File upload failed:", error);
			setLoading(false);
		}
	};

	const handleFileDelete = async (fieldId: string) => {
		const existingFile = uploadedFiles[fieldId];
		if (!existingFile) return;

		try {
		setLoading(true);

		// Optionally add an API call to delete the file from server here, if needed
		// await deleteFileFromServer(existingFile.id);

		setUploadedFiles((prev) => {
			const updated = { ...prev };
			delete updated[fieldId];
			return updated;
		});
	} catch (error) {
		console.error("Error deleting file:", error);
	} finally {
		setLoading(false); // Ensure loading is turned off even if error occurs
	}
	};

	const handleContinue = () => {
		setOpen(false);
		if (activeTab === 1) {
			const repoType = selectedRepo === "github" ? "GIT" : "HF"; // Determine the type
			onContinue({ link, pat, type: repoType }); // Pass the type to the parent
		} else {
			onContinue({ link: "", pat: "", type: "" });
		}
	};


	return (
		<>
			<Drawer
				open={true}
				onClose={handleCloseDrawer}
				anchor="right"
				PaperProps={{
					sx: { width: { xs: "100%", md: 700, lg: 700 } },
					className: "bg-white shadow-lg",
				}}
			>
				<Box sx={{ padding: 3 }}>
					<Typography variant="h6" mb={2} sx={{ fontWeight: "bold", whiteSpace: "nowrap" }}>
						Upload Project Docket
					</Typography>

					<Tabs
						value={activeTab}
						onChange={handleTabChange}
						sx={{ mt: 3, mb: 6 }}
						TabIndicatorProps={{
							style: {
								backgroundColor: 'gray',
								height: '2px',
							},
						}}
					>
						<Tab label="File" className="text-transform-none" sx={{ color: "black !important", fontWeight: "bold", fontSize: "1rem", outline: "none", "&.Mui-selected": { backgroundColor: "#FEF4ED", borderRadius: "8px", color: "black !important" } }} />
						{/* <Tab label="Link" className="text-transform-none" sx={{ color: "black !important", fontWeight: "bold", fontSize: "1rem", outline: "none", "&.Mui-selected": { backgroundColor: "#FEF4ED", borderRadius: "8px", color: "black !important" } }} /> */}
					</Tabs>
					

					{activeTab === 0  && (
						<Box
							sx={{
								position: "relative",
								flexGrow: 1,
								width: "100%",
								height: "calc(85vh - 150px)",
								overflow: "hidden",
								borderRadius: 2,
							}}
						>
							<Image
								src={signinother}
								alt="Form Background"
								layout="fill"
								objectFit="cover"
								className="z-0"
							/>

							<Box
								onDragOver={(e) => e.preventDefault()}
								onDrop={async (e) => {
									e.preventDefault();
									const file = e.dataTransfer.files?.[0];
									if (!file) return;

									const allowedExtensions = /\.(pkl|joblib|pth|h5|onnx)$/i;
									const allowedMimeTypes = [
										'application/octet-stream',
									];

									if (
										allowedExtensions.test(file.name) &&
										allowedMimeTypes.includes(file.type)
									) {
										await handleFileUpload(file);
									} else {
										alert(
											'Invalid file type. Only .pkl, .joblib, .pth, .h5, and .onnx files are allowed.'
										);
									}
								}}
								sx={{
									position: "relative",
									zIndex: 10,
									height: "100%",
									width: "100%",
									display: "flex",
									flexDirection: "column",
									alignItems: "center",
									justifyContent: "center",
									textAlign: "center",
									px: 2,
									border: "2px dashed #F06D1A",
									borderRadius: 2,
									backgroundColor: "rgba(255,255,255,0.85)",
								}}
							>
								<CloudUploadIcon sx={{ fontSize: 50, color:  "#F06D1A" }} />
								<Typography mt={2} sx={{ fontWeight: "600", fontSize: "16px", lineHeight: "24px", fontFamily: '"Open Sans", sans-serif', }}>
									Start by uploading a file
								</Typography>
								<Typography
									variant="body2"
									color="textSecondary"
									mt={1}
									sx={{ fontWeight: 400, color: "#475467", fontSize: "14px", lineHeight: "20px", fontFamily: '"Open Sans", sans-serif', }}
								>
									Start creating by uploading your Docket.
								</Typography>
								<Typography
									variant="body2"
									color="textSecondary"
									mt={0}
									sx={{ fontWeight: 400, color: "#475467", fontSize: "14px", fontFamily: '"Open Sans", sans-serif', }}
								>
									Accepted formats: .pkl, .joblib, .pth, .h5
								</Typography>
								<label htmlFor="file-upload">
									<Box
										onDragOver={(e) => e.preventDefault()}
										onDrop={async (e) => {
											e.preventDefault();
											const file = e.dataTransfer.files?.[0];
											if (!file) return;

											const allowedExtensions = /\.(pkl|joblib|pth|h5|onnx)$/i;
											const allowedMimeTypes = [
												'application/octet-stream',
											];

											if (
												allowedExtensions.test(file.name) &&
												allowedMimeTypes.includes(file.type)
											) {
												await handleFileUpload(file);
											} else {
												alert(
													'Invalid file type. Only .pkl, .joblib, .pth, .h5, and .onnx files are allowed.'
												);
											}
										}}
									>
										<input
											id="file-upload"
											type="file"
											disabled={!!link || loading} 
											accept=".pkl, .joblib, .pth, .h5, .onnx"
											hidden
											onChange={async (e) => {
												const file = e.target.files?.[0];
												e.target.value = "";
												if (file) {
													const allowedExtensions = ['.pkl', '.joblib', '.pth', '.h5', '.onnx'];
													const fileExtension = file.name.split('.').pop()?.toLowerCase();

													if (fileExtension && allowedExtensions.includes(`.${fileExtension}`)) {
														await handleFileUpload(file);
													} else {
														alert('Invalid file type. Please upload a .pkl, .joblib, .pth, .h5, or .onnx file.');
													}
												}
											}}
										/>
										<Button
											disabled={!!link || loading}
											component="label"
											variant="contained"
											className="text-transform-none"
											startIcon={
												<svg
													width="25"
													height="25"
													viewBox="0 0 24 24"
													fill="none"
													xmlns="http://www.w3.org/2000/svg"
												>
													<path
														d="M12 21V11M12 11L9 14M12 11L15 14M7 16.8184C4.69636 16.2074 3 14.1246 3 11.6493C3 9.20008 4.8 6.9375 7.5 6.5C8.34694 4.48637 10.3514 3 12.6893 3C15.684 3 18.1317 5.32251 18.3 8.25C19.8893 8.94488 21 10.6503 21 12.4969C21 14.8148 19.25 16.7236 17 16.9725"
														stroke={loading ? "#C5C5C5" : "#FFFFFF"}
														strokeWidth="2"
														strokeLinecap="round"
														strokeLinejoin="round"
													/>
												</svg>
											}
											sx={{
												marginTop: 3,
												background: loading ? "#C5C5C5" : "linear-gradient(to right, #F45C24, #FFCB80)",
												borderRadius: "5px",
												paddingX: 5,
												paddingY: 1.5,
												fontWeight: "bold",
												height: "40px",
												width: { md: "352px", sm: "350px" },
												whiteSpace: "nowrap",
											}}
										>
											Drag & Drop or Upload
											
											<input
												type="file"
												accept=".pkl, .joblib, .pth, .h5, .onnx"
												disabled={!!link || loading}
												hidden
												onChange={async (e) => {
													const file = e.target.files?.[0];
													e.target.value = "";
													if (file) {
														const allowedExtensions = ['.pkl', '.joblib', '.pth', '.h5', '.onnx'];
														const fileExtension = file.name.split('.').pop()?.toLowerCase();

														if (fileExtension && allowedExtensions.includes(`.${fileExtension}`)) {
															await handleFileUpload(file);
														} else {
															alert('Invalid file type. Please upload a .pkl, .joblib, .pth, .h5, or .onnx file.');
														}
													}
												}}
											/>
										</Button>
										{loading && <div className="flex justify-center items-center h-full">
								<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mt-4" />
							</div>}
									</Box>
								</label>

								{uploadedFiles["upload_file"] && (
									<Box
										sx={{
											mt: 1,
											p: 2,
											border: "1px solid #ddd",
											borderRadius: 2,
											backgroundColor: "#fafafa",
											display: "flex",
											alignItems: "center",
											justifyContent: "space-between",
											width: "100%",
											maxWidth: 400,
										}}
									>
										<Typography variant="body1" sx={{ overflowWrap: "anywhere" }}>
											{uploadedFiles.upload_file?.file_path.length > 30
												? `${uploadedFiles.upload_file.file_path.slice(0, 30)}...`
												: uploadedFiles.upload_file?.file_path}
										</Typography>
										<Button
											onClick={() => handleFileDelete("upload_file")}
											sx={{
												minWidth: 0,
												color: "red",
												fontWeight: "bold",
												ml: 2,
											}}
										>
											✕
										</Button>
									</Box>
								)}
							</Box>
						</Box>
					)}

					{activeTab === 1 && (
						<Box
							sx={{
								position: "relative",
								flexGrow: 1,
								width: "100%",
								minHeight: { xs: "auto", md: "calc(85vh - 150px)" },
								maxHeight: { xs: "none", md: "calc(85vh - 150px)" },
								height: { xs: "auto", md: "calc(85vh - 150px)" },
								overflow: { xs: "visible", md: "hidden" },
								borderRadius: 2,
							}}
						>
							<Box sx={{ marginTop: 3 }}>
								<RadioGroup
									value={selectedRepo}
									onChange={(e) => setSelectedRepo(e.target.value)}
								>
									<Box
										sx={{
											border:
												selectedRepo === "github"
													? "2px solid #F06D1A"
													: "1px solid #ccc",
											borderRadius: 2,
											padding: 2,
											marginBottom: 2,
											width: { xs: "100%", sm: "100%", md: "650px" },
											height: { xs: "auto", md: "98px" },
											backgroundColor: "#F8FBFF"
										}}
									>
										<FormControlLabel
											value="github"
											control={<Radio />}
											label={
												<Box display="flex" alignItems="center" marginLeft="14px" marginBottom="14px">
													<Box sx={{
														backgroundColor: '#DBEAFE',
														padding: '6px',
														borderRadius: '8px',
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														marginRight: '10px',
													}}>
														<svg
															width="40"
															height="40"
															viewBox="0 0 20 20"
															fill="currentColor"
															xmlns="http://www.w3.org/2000/svg"
															style={{ marginLeft: 1 }}
														><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <title>github [#142]</title> <desc>Created with Sketch.</desc> <defs> </defs> <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd"> <g id="Dribbble-Light-Preview" transform="translate(-140.000000, -7559.000000)" fill="#000000"> <g id="icons" transform="translate(56.000000, 160.000000)"> <path d="M94,7399 C99.523,7399 104,7403.59 104,7409.253 C104,7413.782 101.138,7417.624 97.167,7418.981 C96.66,7419.082 96.48,7418.762 96.48,7418.489 C96.48,7418.151 96.492,7417.047 96.492,7415.675 C96.492,7414.719 96.172,7414.095 95.813,7413.777 C98.04,7413.523 100.38,7412.656 100.38,7408.718 C100.38,7407.598 99.992,7406.684 99.35,7405.966 C99.454,7405.707 99.797,7404.664 99.252,7403.252 C99.252,7403.252 98.414,7402.977 96.505,7404.303 C95.706,7404.076 94.85,7403.962 94,7403.958 C93.15,7403.962 92.295,7404.076 91.497,7404.303 C89.586,7402.977 88.746,7403.252 88.746,7403.252 C88.203,7404.664 88.546,7405.707 88.649,7405.966 C88.01,7406.684 87.619,7407.598 87.619,7408.718 C87.619,7412.646 89.954,7413.526 92.175,7413.785 C91.889,7414.041 91.63,7414.493 91.54,7415.156 C90.97,7415.418 89.522,7415.871 88.63,7414.304 C88.63,7414.304 88.101,7413.319 87.097,7413.247 C87.097,7413.247 86.122,7413.234 87.029,7413.87 C87.029,7413.87 87.684,7414.185 88.139,7415.37 C88.139,7415.37 88.726,7417.2 91.508,7416.58 C91.513,7417.437 91.522,7418.245 91.522,7418.489 C91.522,7418.76 91.338,7419.077 90.839,7418.982 C86.865,7417.627 84,7413.783 84,7409.253 C84,7403.59 88.478,7399 94,7399" id="github-[#142]"> </path> </g> </g> </g> </g></svg>
													</Box>
													<Box display="flex" flexDirection="column" justifyContent="center" marginLeft="20px">
														<Typography sx={{ fontWeight: 700, fontSize: '18px', lineHeight: '24px' }}>
															Import GitHub repository
														</Typography>
														<Typography
															sx={{
																fontSize: '12px',
																color: '#767676',
																lineHeight: '20px',
															}}
														>
															Create from a GitHub repository archive.<br /> Use the repo URL or any deep link.
														</Typography>
													</Box>
												</Box>
											}
										/>
									</Box>
									<Box
										sx={{
											border:
												selectedRepo === "huggingface"
													? "2px solid #F06D1A"
													: "1px solid #ccc",
											borderRadius: 2,
											padding: 2,
											marginBottom: 2,
											width: { xs: "100%", sm: "100%", md: "650px" },
											height: { xs: "auto", md: "98px" },
											backgroundColor: "#F8FBFF"
										}}
									>
										<FormControlLabel
											value="huggingface"
											control={<Radio />}
											label={
												<Box display="flex" alignItems="center" marginLeft="14px" marginBottom="14px">
													<Box sx={{
														backgroundColor: '#DBEAFE',
														padding: '6px',
														borderRadius: '8px',
														display: 'flex',
														alignItems: 'center',
														justifyContent: 'center',
														marginRight: '10px',
													}}>
														<img
															src="/huggingface.svg"
															alt="Hugging Face Logo"
															className="w-[60px] h-[50px] sm:w-[40px] sm:h-[40px]"
														/>
													</Box>
													<Box display="flex" flexDirection="column" justifyContent="center" marginLeft="20px">
														<Typography sx={{ fontWeight: 700, fontSize: '18px', lineHeight: '24px' }}>
															Import Huggingface repository
														</Typography>
														<Typography
															sx={{
																fontSize: '12px',
																color: '#767676',
																lineHeight: '20px',
															}}
														>
															Create from a Huggingface repository archive.<br /> Use the repo URL or any deep link.
														</Typography>
													</Box>
												</Box>
											}
										/>
									</Box>
								</RadioGroup>

								<Typography className="mb-2" sx={{ fontWeight: "600", fontSize: "16px", lineHeight: "21px", color: "#161616", mb: 2 }}>
									URL
								</Typography>

								<Typography sx={{ fontWeight: "400", fontSize: "1rem", lineHeight: "21px", color: "#666666", marginBottom: "0.7rem", mt: 1 }}>
									Remote URL
								</Typography>

								<TextField
									placeholder="Enter remote URL"
									value={link}
									onChange={(e) => setLink(e.target.value)}
									fullWidth
									InputProps={{
										sx: {
											height: '42px',
											alignItems: 'center',
											padding: "13px 14px",
											color: "#161616",
											paddingLeft: "0px",
											marginBottom: "10px"
										},
									}}
								/>

								<Typography sx={{ fontWeight: "400", fontSize: "1rem", lineHeight: "21px", color: "#666666", marginBottom: "0.1rem", mt: 1 }}>
									PAT
								</Typography>

								<TextField
									placeholder="Enter PAT"
									value={pat}
									onChange={(e) => setPat(e.target.value)}
									fullWidth
									margin="normal"
									InputProps={{
										sx: {
											height: '42px',
											alignItems: 'center',
											padding: "13px 14px",
											color: "#161616",
											paddingLeft: "0px"
										},
									}}
								/>
							</Box>
						</Box>
					)}
				</Box>


				<Box sx={{ padding: 3, display: "flex", justifyContent: "space-evenly", borderTop: "1px solid #eee", gap: 2 }}>
					<Button variant="outlined" onClick={handleCloseDrawer} sx={{ fontWeight: "bold", width: "150px", color: "#F06D1A", border: "1px solid #F06D1A" }}>
						Back
					</Button>
					<Button onClick={handleContinue} variant="contained" sx={{ background: "linear-gradient(to right, #F45C24, #FFCB80)", borderRadius: "5px", paddingX: 5, fontWeight: "bold" }}>
						Continue
					</Button>
				</Box>
			</Drawer>

		</>
	);
}