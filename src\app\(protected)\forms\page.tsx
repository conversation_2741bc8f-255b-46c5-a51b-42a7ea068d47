"use client";

import type React from "react";
import {
	Paper,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
	IconButton,
} from "@mui/material";
import EditIcon from "@mui/icons-material/Edit";
// import VisibilityIcon from "@mui/icons-material/Visibility";
// import CustomButton from "../../components/ui/Button";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { templateGetList } from "../../../services/desApiService";

const FormsPage: React.FC = () => {
	const router = useRouter();
	const [formList, setFormList] = useState<{ id: string; name: string; type: number }[]>([]);
	const [loading, setLoading] = useState(true);
	const [, setError] = useState(false);

	useEffect(() => {
		const fetchTemplate = async () => {
			try {
				const response = await templateGetList();
				setFormList(response?.data || []);
			} catch (error) {
				console.error("Failed to fetch data:", error);
				setError(true);
			} finally {
				setLoading(false);
			}
		};

		fetchTemplate();
	}, []);

	// const handleAddForm = () => {
	// 	router.push("/forms/addform");
	// };

	const handleEdit = (id: string, type: number, name: string) => {
		router.push(`/forms/create?type=${type}&&id=${id}&&name=${name}`);
	};

	return (
		<>
			<div className="bg-white p-6 min-h-screen">
				<div className="flex justify-between items-center pb-3">
					<h1 className="text-2xl font-bold font-[Open_Sans] text-[#000000]">Forms</h1>
					{/* <div className="flex justify-end mt-4">
            <CustomButton text="Add Form" onClick={handleAddForm} isLoading={false} />
          </div> */}
				</div>

				{/* Forms Table */}
				<div className="flex justify-center mt-12">
					<TableContainer
						component={Paper}
						// style={{ height: "37rem", overflowX: "auto", width: "100%" }}
						className="border border-gray-300 shadow-md rounded-md max-w-5xl w-full"
					>
						{loading ? (
							<div className="flex justify-center items-center h-full">
								<div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500" />
							</div>
						) : formList.length === 0 ? (
							<div className="flex justify-center items-center w-full h-64 text-xl text-gray-500">
								No data available
							</div>
						) : (
							<Table sx={{ "& .MuiTableRow-root": { height: "40px" } }}>
								<TableHead>
									<TableRow>
										<TableCell
											sx={{
												fontWeight: "bold",
												fontSize: "16px",
												paddingLeft: "16px",
												color: "#475569",
												whiteSpace:"nowrap"
											}}
										>
											S.No
										</TableCell>
										<TableCell
											sx={{
												fontWeight: "bold",
												fontSize: "16px",
												padding: "12px",
												color: "#475569",
												whiteSpace:"nowrap"
											}}
										>
											Form Name
										</TableCell>
										<TableCell
											sx={{
												fontWeight: "bold",
												fontSize: "16px",
												padding: "12px",
												color: "#475569",
												whiteSpace:"nowrap"
											}}
										>
											Actions
										</TableCell>
									</TableRow>
								</TableHead>
								<TableBody>
									{formList.map((form, index) => (
										<TableRow key={form.id} className="odd:bg-[#FEF4ED]">
											<TableCell className="pl-4" sx={{ padding: "20px" }}>
												{index + 1}
											</TableCell>
											<TableCell sx={{ padding: "8px" ,whiteSpace:"nowrap" }}>{form.name}</TableCell>
											<TableCell sx={{ padding: "8px",whiteSpace:"nowrap" }}>
												<div className="flex gap-x-3">
													<IconButton
														sx={{
															backgroundColor: "#CFE6E2",
															color: "#4bccb4",
															width: "32px",
															height: "32px",
															"&:hover": {
																backgroundColor: "#B8D4CF",
															},
														}}
														onClick={() => handleEdit(form?.id, form?.type,form?.name)}
													>
														<EditIcon fontSize="small" />
													</IconButton>
													{/* <IconButton
                            sx={{
                              backgroundColor: "#FCD9C3",
                              color: "#eb975b",
                              width: "32px",
                              height: "32px",
                              "&:hover": {
                                backgroundColor: "#FBC0A3",
                              },
                            }}
                          >
                            <VisibilityIcon fontSize="small" />
                          </IconButton> */}
												</div>
											</TableCell>
										</TableRow>
									))}
								</TableBody>
							</Table>
						)}
					</TableContainer>
				</div>
			</div>
		</>
	);
};

export default FormsPage;
