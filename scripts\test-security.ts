#!/usr/bin/env ts-node

/**
 * Security Test Script (TypeScript)
 * Tests OWASP compliance and security headers
 */

import * as https from 'https';
import * as http from 'http';

// Configuration
const TEST_URL: string = process.env.TEST_URL || 'http://localhost:3000';
const TIMEOUT: number = 10000;

// Type definitions
interface SecurityTestResult {
  test: string;
  passed: boolean;
  value?: string;
  message?: string;
}

interface TestResponse {
  statusCode: number;
  headers: http.IncomingHttpHeaders;
  body: string;
}

interface TestReport {
  passed: boolean;
  score: number;
}

// Expected security headers
const REQUIRED_HEADERS: Record<string, string> = {
  'content-security-policy': 'Content Security Policy',
  'x-frame-options': 'X-Frame-Options',
  'x-content-type-options': 'X-Content-Type-Options',
  'x-xss-protection': 'X-XSS-Protection',
  'referrer-policy': 'Referrer Policy',
  'permissions-policy': 'Permissions Policy'
};

// CSP directives that should be present
const REQUIRED_CSP_DIRECTIVES: string[] = [
  'default-src',
  'script-src',
  'style-src',
  'object-src',
  'base-uri',
  'frame-ancestors'
];

// Dangerous CSP values (for production)
const DANGEROUS_CSP_VALUES: string[] = [
  "'unsafe-inline'",
  "'unsafe-eval'",
  "*"
];

/**
 * Make HTTP/HTTPS request to test URL
 * @param url - URL to test
 * @returns Promise with response data
 */
function makeRequest(url: string): Promise<TestResponse> {
  return new Promise((resolve, reject) => {
    const client = url.startsWith('https:') ? https : http;
    
    const req = client.get(url, { timeout: TIMEOUT }, (res) => {
      let data = '';
      res.on('data', (chunk: Buffer) => data += chunk.toString());
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode || 0,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

/**
 * Test security headers presence and validity
 * @param headers - HTTP headers to test
 * @returns Array of test results
 */
function testSecurityHeaders(headers: http.IncomingHttpHeaders): SecurityTestResult[] {
  const results: SecurityTestResult[] = [];
  
  // Test for required headers
  Object.entries(REQUIRED_HEADERS).forEach(([headerKey, headerName]) => {
    const headerValue = headers[headerKey];
    if (headerValue) {
      results.push({
        test: `${headerName} Present`,
        passed: true,
        value: Array.isArray(headerValue) ? headerValue.join(', ') : headerValue
      });
    } else {
      results.push({
        test: `${headerName} Present`,
        passed: false,
        message: `Missing ${headerName} header`
      });
    }
  });

  return results;
}

/**
 * Test Content Security Policy header
 * @param cspHeader - CSP header value
 * @returns Array of test results
 */
function testCSP(cspHeader: string | string[] | undefined): SecurityTestResult[] {
  const results: SecurityTestResult[] = [];
  
  // Convert array to string if needed
  const cspValue = Array.isArray(cspHeader) ? cspHeader.join(', ') : cspHeader;
  
  if (!cspValue) {
    results.push({
      test: 'CSP Header Present',
      passed: false,
      message: 'No CSP header found'
    });
    return results;
  }

  results.push({
    test: 'CSP Header Present',
    passed: true,
    value: cspValue.substring(0, 100) + '...'
  });

  // Test for required directives
  REQUIRED_CSP_DIRECTIVES.forEach(directive => {
    if (cspValue.includes(directive)) {
      results.push({
        test: `CSP ${directive} directive`,
        passed: true
      });
    } else {
      results.push({
        test: `CSP ${directive} directive`,
        passed: false,
        message: `Missing ${directive} directive`
      });
    }
  });

  // Test for dangerous values in production
  if (process.env.NODE_ENV === 'production') {
    DANGEROUS_CSP_VALUES.forEach(value => {
      if (cspValue.includes(value)) {
        results.push({
          test: `CSP ${value} check`,
          passed: false,
          message: `Dangerous value ${value} found in production CSP`
        });
      } else {
        results.push({
          test: `CSP ${value} check`,
          passed: true
        });
      }
    });
  }

  return results;
}

/**
 * Generate and display test report
 * @param results - Array of test results
 * @returns Test report summary
 */
function generateReport(results: SecurityTestResult[]): TestReport {
  const passed = results.filter(r => r.passed).length;
  const total = results.length;
  const percentage = Math.round((passed / total) * 100);

  console.log('\n🔒 Security Test Report');
  console.log('========================\n');
  console.log(`Overall Score: ${passed}/${total} (${percentage}%)`);
  console.log(`Status: ${passed === total ? '✅ PASSED' : '❌ FAILED'}\n`);

  // Group results by category
  const categories: Record<string, SecurityTestResult[]> = {
    'Security Headers': results.filter(r => r.test.includes('Present') && !r.test.includes('CSP')),
    'Content Security Policy': results.filter(r => r.test.includes('CSP')),
    'Other': results.filter(r => !r.test.includes('Present') && !r.test.includes('CSP'))
  };

  Object.entries(categories).forEach(([category, categoryResults]) => {
    if (categoryResults.length === 0) return;
    
    console.log(`${category}:`);
    console.log('-'.repeat(category.length + 1));
    
    categoryResults.forEach(result => {
      const status = result.passed ? '✅' : '❌';
      console.log(`${status} ${result.test}`);
      
      if (result.value) {
        console.log(`   Value: ${result.value}`);
      }
      
      if (result.message) {
        console.log(`   Issue: ${result.message}`);
      }
    });
    
    console.log('');
  });

  return { passed: passed === total, score: percentage };
}

/**
 * Main function to run security tests
 */
async function runSecurityTests(): Promise<void> {
  try {
    console.log(`🔍 Testing security headers for: ${TEST_URL}`);
    console.log('Please ensure the application is running...\n');

    const response = await makeRequest(TEST_URL);
    
    if (response.statusCode !== 200) {
      console.error(`❌ Request failed with status: ${response.statusCode}`);
      process.exit(1);
    }

    const headerResults = testSecurityHeaders(response.headers);
    const cspResults = testCSP(response.headers['content-security-policy']);
    
    const allResults = [...headerResults, ...cspResults];
    const report = generateReport(allResults);

    // Exit with appropriate code
    process.exit(report.passed ? 0 : 1);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('❌ Security test failed:', errorMessage);
    console.log('\nTroubleshooting:');
    console.log('1. Ensure the application is running');
    console.log('2. Check the TEST_URL environment variable');
    console.log('3. Verify network connectivity');
    process.exit(1);
  }
}

// Export functions for testing
export {
  runSecurityTests,
  testSecurityHeaders,
  testCSP,
  type SecurityTestResult,
  type TestResponse,
  type TestReport
};

// Run tests if this script is executed directly
if (require.main === module) {
  runSecurityTests().catch(console.error);
}
