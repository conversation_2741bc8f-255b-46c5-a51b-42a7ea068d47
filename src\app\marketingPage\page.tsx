"use client";

import { <PERSON><PERSON>, Typography } from "@mui/material";
import Image from "next/image";
import botlogo from "../../assests/Group **********.png";
import SingHealthLogo from "../../assests/singhealth-group-logo-vector 1.png";
import { useRouter } from "next/navigation";
import Link from 'next/link';

const MarketingPage = () => {
	const router = useRouter();

	return (
		<div className="relative w-full bg-white">
			{/* Background Banner */}
			<div
				className="relative w-full h-[300px] lg:h-[400px] border-b-10 "
				style={{ borderBottomColor: "#FDBC74" }}
			>
				<Image
					src={botlogo}
					alt="Banner Image"
					layout="fill"
					objectFit="cover"
					className="absolute z-0"
				/>
				<div className="absolute inset-0 bg-opacity-50 flex items-center justify-center text-center px-4">
					<Typography
						variant="h5"
						className="text-white font-semibold max-w-2xl"
					>
						Join a growing community of researchers and organizations to submit,
						evaluate, and refine AI models for diverse use cases.
					</Typography>
				</div>
			</div>

			{/* Content Section */}
			<div className="relative py-12 text-center">
				<Image
					src={SingHealthLogo} // Update with actual path
					alt="SingHealth Logo"
					className="mx-auto mb-4"
					style={{ width: "200px", height: "170px" }} // Adjust size as needed
				/>
				<Typography variant="h2" sx={{ fontWeight: "600",fontFamily:"open sans",lineHeight:"71.17px",letterSpacing:"-4%", alignContent:"center" }}>
					AI Community Portal – Collaborate, Evaluate, Innovate
				</Typography>
				<div className="pt-2 mt-3 mx-auto text-center w-full max-w-7xl md:px-0 px-4">
					<Typography variant="body1" sx={{ fontWeight: "400", fontFamily: "open sans", color: " #161616", lineHeight: "27.68px",fontSize: "19px" }}>


						The AIMx AI Community Portal enables organizations and researchers
						to collaborate by submitting AI models, driving innovation across
						industries. Our platform ensures secure, scalable, and efficient
						model evaluation for optimal outcomes.
					</Typography>
				</div>
				<div className="mt-6 flex flex-col sm:flex-row sm:justify-center items-center gap-4 pt-4 ">


					<Link href="/landingPage" prefetch>
						<Button
							variant="outlined"
							className="text-transform-none"
							style={{
								border: "1px solid #16AFB5",
								color: "#16AFB5",
								padding: "10px",
								width: "150px",
								fontWeight: "bold",
								fontSize: "16px"

							}}
						>
							Login
						</Button>
					</Link>
					<Button
						variant="contained"
						onClick={() => (router.push("/register-organization"))}
						style={{ padding: "10px", width: "280px", fontWeight: "bold", fontSize: "16px" }}
						className="w-full font-bold sm:w-auto px-6 py-3 bg-gradient-to-r from-[#F45C24] to-[#FFCB80] text-white text-transform-none"

					>
						Register Your Organization
					</Button>
				</div>
			</div>
		</div>
	);
};

export default MarketingPage;
