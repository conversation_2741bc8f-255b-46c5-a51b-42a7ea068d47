// import { createApiClient } from "../lib/createApiClient";
// import <PERSON><PERSON> from "js-cookie";

// export const pushApi = createApiClient("http://13.229.196.7:8089/api/v1/system/notifications");

// export const postMessage = async () => {
//   try {
//     const userId = Cookie.get("user_id");

//     if (!userId) {
//       throw new Error("Missing user ID");
//     }

//     const response = await pushApi.post("/post-message", {
//       user_id: userId,
//       message: "successfully updated the profile picture"
//     });

//     console.log("Notification posted:", response.data);
//     return response;
//   } catch (error) {
//     console.error("Error posting notification:", error.response?.data || error.message || error);
//     throw error;
//   }
// };