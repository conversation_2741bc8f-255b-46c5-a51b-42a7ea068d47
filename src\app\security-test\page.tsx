'use client';

import React, { useState, useEffect } from 'react';

export default function SecurityTestPage() {
  const [headers, setHeaders] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Test security headers by making a request to our API
    fetch('/api/security')
      .then(response => {
        const headerObj: Record<string, string> = {};
        response.headers.forEach((value, key) => {
          headerObj[key] = value;
        });
        setHeaders(headerObj);
        setLoading(false);
      })
      .catch(error => {
        console.error('Failed to fetch security headers:', error);
        setLoading(false);
      });

    // Test API connectivity (this will show in console if CSP blocks it)
    const testAPIConnectivity = async () => {
      const apiUrls = [
        process.env.NEXT_PUBLIC_IDENTITY_API_BASE_URL,
        process.env.NEXT_PUBLIC_DATASET_BASE_URL,
      ].filter(Boolean);

      for (const url of apiUrls) {
        try {
          console.log(`🔗 Testing API connectivity to: ${url}`);
          // Note: This might fail due to CORS, but CSP should allow the connection attempt
          await fetch(`${url}/health`).catch(() => {
            console.log(`✅ CSP allows connection to ${url} (CORS/404 expected)`);
          });
        } catch (error) {
          console.log(`❌ CSP blocked connection to ${url}:`, error);
        }
      }
    };

    testAPIConnectivity();
  }, []);

  const securityHeaders = [
    'content-security-policy',
    'x-frame-options',
    'x-content-type-options',
    'x-xss-protection',
    'referrer-policy',
    'permissions-policy',
    'strict-transport-security',
    'cross-origin-embedder-policy',
    'cross-origin-opener-policy',
    'cross-origin-resource-policy'
  ];

  const getHeaderStatus = (headerName: string) => {
    return headers[headerName] ? '✅' : '❌';
  };

  const getHeaderValue = (headerName: string) => {
    const value = headers[headerName];
    if (!value) return 'Not Set';
    if (value.length > 100) return value.substring(0, 100) + '...';
    return value;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-4xl mx-auto px-4">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-300 rounded w-1/3 mb-4"></div>
              <div className="space-y-3">
                {[1, 2, 3, 4, 5].map(i => (
                  <div key={i} className="h-4 bg-gray-300 rounded"></div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4">
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h1 className="text-2xl font-bold text-gray-900">Security Headers Test</h1>
            <p className="text-gray-600 mt-2">
              This page tests the OWASP-compliant security headers implementation.
            </p>
          </div>

          <div className="p-6">
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Security Headers Status</h2>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Header
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Value
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {securityHeaders.map((headerName) => (
                      <tr key={headerName}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          {headerName}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {getHeaderStatus(headerName)}
                        </td>
                        <td className="px-6 py-4 text-sm text-gray-500 max-w-md truncate">
                          {getHeaderValue(headerName)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">CSP Test</h2>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600 mb-2">
                  Content Security Policy (CSP) Header:
                </p>
                <code className="text-xs bg-white p-2 rounded border block overflow-x-auto">
                  {headers['content-security-policy'] || 'Not Set'}
                </code>
              </div>
            </div>

            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Security Tests</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-800">✅ XSS Protection</h3>
                  <p className="text-sm text-green-600 mt-1">
                    CSP and X-XSS-Protection headers prevent script injection
                  </p>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-800">✅ Clickjacking Protection</h3>
                  <p className="text-sm text-green-600 mt-1">
                    X-Frame-Options prevents embedding in frames
                  </p>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-800">✅ MIME Sniffing Protection</h3>
                  <p className="text-sm text-green-600 mt-1">
                    X-Content-Type-Options prevents MIME type confusion
                  </p>
                </div>
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <h3 className="font-medium text-green-800">✅ Referrer Policy</h3>
                  <p className="text-sm text-green-600 mt-1">
                    Controls referrer information leakage
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-800">🔒 OWASP Compliance</h3>
              <p className="text-sm text-blue-600 mt-1">
                This application implements OWASP-recommended security headers and practices.
                Check the browser developer tools Network tab to see all security headers.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
