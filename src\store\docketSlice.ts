// store/docketSlice.ts

import { createSlice } from "@reduxjs/toolkit";
import type { PayloadAction } from "@reduxjs/toolkit";

interface DocketState {
    docketList: Record<string, unknown>;
    loading: boolean;
}

const initialState: DocketState = {
    docketList: {
        projectId: "",
        modelName: "",
        specialty: "",
        subSpecialty: "",
        type: "",
    },
    loading: false,
};

const docketSlice = createSlice({
    name: "docket",
    initialState,
    reducers: {
        setDocketList: (state, action: PayloadAction<Record<string, unknown>>) => {
            state.docketList = action.payload;
        },
        setDocketLoading: (state, action: PayloadAction<boolean>) => {
            state.loading = action.payload;
        },
        clearDocketList: (state) => {
            state.docketList = {};
        },
    },
});

export const { setDocketList, setDocketLoading, clearDocketList } = docketSlice.actions;
export default docketSlice.reducer;
