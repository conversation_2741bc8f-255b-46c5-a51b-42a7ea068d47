importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.13.2/firebase-messaging-compat.js');
if (typeof firebase === 'undefined') {
    throw new Error('Firebase SDK failed to load');
}
const firebaseConfig = {
    apiKey: "AIzaSyAFXYE-zGKbl131J43TcOW8Kw9j2a56fYM",
    authDomain: "aimx-a7d32.firebaseapp.com",
    projectId: "aimx-a7d32",
    storageBucket: "aimx-a7d32.firebasestorage.app",
    messagingSenderId: "1048421165508",
    appId: "1:1048421165508:web:add7f684b909a6f2645c91",
    measurementId: "G-TVZ8F7KC79"
};
try {
    firebase.initializeApp(firebaseConfig);
}
catch (error) {
    console.error('Failed to initialize Firebase:', error);
    throw error;
}
try {
    firebase.messaging();
}
catch (error) {
    console.error('Failed to initialize Firebase Messaging:', error);
    throw error;
}
function sanitizeString(input) {
    if (typeof input !== 'string')
        return '';
    return input
        .replace(/[<>"'&]/g, '')
        .substring(0, 200)
        .trim();
}
function sanitizeUrl(input) {
    if (typeof input !== 'string')
        return null;
    try {
        const url = new URL(input);
        if (url.protocol === 'https:' &&
            (url.hostname.endsWith('.gstatic.com') ||
                url.hostname.endsWith('.googleapis.com') ||
                url.hostname === 'localhost')) {
            return url.href;
        }
    }
    catch (error) {
        console.warn('Invalid URL provided:', input);
        console.warn(error);
    }
    return null;
}
function isValidPayload(payload) {
    return payload !== null &&
        typeof payload === 'object' &&
        payload !== undefined;
}
self.addEventListener('push', (event) => {
    var _a;
    const payload = ((_a = event.data) === null || _a === void 0 ? void 0 : _a.json()) || {};
    try {
        if (!isValidPayload(payload)) {
            console.warn('Invalid payload received:', payload);
            return;
        }
        const notification = payload.notification || {};
        const title = sanitizeString(notification.title) || 'New Notification';
        const body = sanitizeString(notification.body) || '';
        const icon = sanitizeUrl(notification.icon) || '/favicon.ico';
        console.log('[firebase-messaging-sw.ts] Received background message:', {
            title,
            body: body.substring(0, 100)
        });
        const notificationOptions = {
            body: body,
            icon: icon,
            badge: '/favicon.ico',
            tag: 'aimx-notification',
            requireInteraction: false,
            silent: false,
            data: {
                timestamp: Date.now(),
                source: 'firebase',
                originalData: payload.data ? JSON.stringify(payload.data).substring(0, 500) : null
            }
        };
        self.registration.showNotification(title, notificationOptions)
            .catch((error) => {
            console.error('Failed to show notification:', error);
        });
    }
    catch (error) {
        console.error('Error processing background message:', error);
    }
});
self.addEventListener('error', (event) => {
    console.error('Service Worker Error:', event.error);
});
self.addEventListener('unhandledrejection', (event) => {
    console.error('Service Worker Unhandled Promise Rejection:', event.reason);
    event.preventDefault();
});
self.addEventListener('activate', (event) => {
    console.log('Firebase messaging service worker activated');
    event.waitUntil(self.clients.claim());
});
self.addEventListener('install', (event) => {
    console.log('Firebase messaging service worker installed');
    event.waitUntil(self.skipWaiting());
});
export {};
